"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { hostAgentEndpoint } from "./config";
import { initializeHostAgentOptions } from "./utils/hostAgentOptionsHelper";
import MainLayout from "../components/main-layout";

interface Conversation {
  conversationId: string;
  uid: string;
  firstMessageText: string;
  updatedAt: number;
}

type ConversationList = Conversation[];

export default function LogsPage() {
  const [conversations, setConversations] = useState<Conversation[]>([]);

  useEffect(() => {
    const customHostAgentConversationListUrl = localStorage.getItem(
      "x-hostagent-conversation-list-url"
    );
    const hostAgentOptions = initializeHostAgentOptions();
    if (customHostAgentConversationListUrl) {
      fetch("/api/chat_proxy", {
        method: "POST",
        headers: {
          ...hostAgentOptions,
          "x-url": customHostAgentConversationListUrl,
          "Content-Type": "application/json",
        },
        body: `{"count": -1,"offset": 0,"version": "3.0.0"}`,
      })
        .then((response) => response.json())
        .then((text: any) => {
          const data = JSON.parse(text);
          const conversationList: ConversationList =
            data.data.conversations.map((it: any) => {
              return {
                conversationId: it.conversation_id,
                uid: "",
                firstMessageText: it.title,
                updatedAt: new Date(it.update_time).toLocaleString(),
              };
            });
          setConversations(conversationList);
        })
        .catch((error) => {
          console.error("Error fetching conversations:", error);
        });
    } else {
      fetch(hostAgentEndpoint + "/conversation_list", {
        method: "POST",
        credentials: "include",
      })
        .then((response) => response.json())
        .then((data: ConversationList) => {
          setConversations(data.reverse());
        })
        .catch((error) => {
          console.error("Error fetching conversations:", error);
        });
    }
  }, []);

  return (
    <MainLayout>
      <div className="py-8">
        <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <h1 className="text-2xl font-bold text-gray-900 mb-6">历史对话列表</h1>
        <div className="space-y-4">
          {conversations.map((conversation) => (
            <Link
              key={conversation.conversationId}
              target="_blank"
              href={`/chat?conversation_id=${conversation.conversationId}&uid=${conversation.uid}`}
              className="block p-6 bg-white rounded-lg shadow-sm border border-gray-200 hover:border-blue-500 transition-colors duration-200"
            >
              <div className="flex justify-between items-start mb-2">
                <h2 className="text-lg font-semibold text-gray-900 line-clamp-1">
                  <ReactMarkdown
                    key={conversation.conversationId}
                    remarkPlugins={[remarkGfm]}
                  >
                    {conversation.firstMessageText
                      .trim()
                      .replace(/\n/g, "\n\n")}
                  </ReactMarkdown>
                </h2>
                <span className="text-sm text-gray-500">
                  {conversation.updatedAt}
                </span>
              </div>
              <div className="text-sm text-gray-600">
                对话ID: {conversation.conversationId}
              </div>
            </Link>
          ))}
        </div>
        </div>
      </div>
    </MainLayout>
  );
}
