export interface HostAgentOptions {
  "x-hostagent-url"?: string;
  "x-hostagent-useproxy"?: "true" | "false";
  "x-hostagent-conversation-list-url"?: string;
  "x-hostagent-conversation-item-url"?: string;
  // Headers are dynamic, and other keys might be added from localStorage
  [key: string]: string | undefined;
}

export function initializeHostAgentOptions(): HostAgentOptions {
  const hostAgentOptions: Record<string, any> = {};

  const hostAgentUrl = localStorage.getItem("x-hostagent-url");
  const hostAgentUseProxy = localStorage.getItem("x-hostagent-useproxy");
  const hostAgentHeadersStr = localStorage.getItem("x-hostagent-headers");
  const conversationListUrl = localStorage.getItem(
    "x-hostagent-conversation-list-url"
  );
  const conversationItemUrl = localStorage.getItem(
    "x-hostagent-conversation-item-url"
  );

  if (hostAgentUrl) {
    hostAgentOptions["x-hostagent-url"] = hostAgentUrl;
  }

  if (hostAgentUseProxy) {
    hostAgentOptions["x-hostagent-useproxy"] = hostAgentUseProxy;
  }

  if (conversationListUrl) {
    hostAgentOptions["x-hostagent-conversation-list-url"] = conversationListUrl;
  }

  if (conversationItemUrl) {
    hostAgentOptions["x-hostagent-conversation-item-url"] = conversationItemUrl;
  }

  if (hostAgentHeadersStr) {
    try {
      const hostAgentHeaders = JSON.parse(hostAgentHeadersStr);
      if (Array.isArray(hostAgentHeaders)) {
        for (let i = 0; i < hostAgentHeaders.length; i++) {
          const header = hostAgentHeaders[i];
          if (header.key && header.value) {
            hostAgentOptions[header.key] = header.value;
          }
        }
      }
    } catch (e) {
      console.error("Failed to parse host agent headers:", e);
    }
  }

  return hostAgentOptions;
}