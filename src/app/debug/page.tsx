"use client";

import MainLayout from '../components/main-layout';

export default function DebugPage() {
  return (
    <MainLayout>
      <div className="relative overflow-auto" style={{ height: '100vh' }}>
        <iframe
          src="https://fornax.bytedance.net/space/7527962177773240321/analytics/log-query?env=online"
          style={{ 
            border: 'none',
            position: 'absolute',
            top: '-80px',  // 隐藏顶部导航
            left: '-240px', // 减少左侧隐藏，避免遮挡内容
            width: 'calc(100vw + 240px)', // 调整宽度补偿
            height: 'calc(100vh + 80px)', // 使用视窗高度
            minWidth: '1400px' // 确保最小宽度
          }}
          title="链路追踪监控"
          allow="fullscreen"
        />
      </div>
    </MainLayout>
  );
}