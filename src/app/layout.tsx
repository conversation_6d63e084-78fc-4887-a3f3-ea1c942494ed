import type { Metadata } from "next";
import "./globals.css";

export const metadata: Metadata = {
  title: "Nuro Agent WebUI",
  description: "Nuro Agent WebUI",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh">
      <head>
        <base target="_blank" />
      </head>
      <body>{children}</body>
    </html>
  );
}
