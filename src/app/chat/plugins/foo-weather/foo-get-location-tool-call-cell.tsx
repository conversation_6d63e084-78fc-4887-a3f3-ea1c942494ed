import React, { useState } from "react";
import { ToolCallCellRendererProps } from "../../plugin-system/plugin-types";

interface GetLocationToolArgs {
  reason?: string;
}

export const FooGetLocationToolCallCell: React.FC<
  ToolCallCellRendererProps
> = ({ message }) => {
  const [permissionGranted, setPermissionGranted] = useState<boolean | null>(
    null
  );

  let toolArgs: GetLocationToolArgs = {};
  try {
    toolArgs = JSON.parse(message.toolArgs ?? "{}") ?? {};
  } catch (e) {
    console.error("Failed to parse toolArgs for get_location:", e);
  }

  const reason = toolArgs.reason || "请求获取您的地理位置";

  const handleAllow = async () => {
    setPermissionGranted(true);
    // Simulate fetching location and updating the tool call message with the result
    const simulatedLocation = "广州市";
    const result = {
      content: [{ type: "text", text: simulatedLocation }],
    };
    try {
      message.sendToolCallResult(JSON.stringify(result));
    } catch (error) {
      console.error("Failed to update tool call message:", error);
    }
  };

  const handleDeny = async () => {
    setPermissionGranted(false);
    const result = {
      content: [{ type: "text", text: "用户拒绝了地理位置请求。" }],
    };
    try {
      message.sendToolCallResult(JSON.stringify(result));
    } catch (error) {
      console.error("Failed to update tool call message with denial:", error);
    }
  };

  if (message.toolResult) {
    let parsedResult: { content?: [{ type: "text"; text: string }] } = {};
    try {
      parsedResult = JSON.parse(message.toolResult);
    } catch (e) {}
    const location = parsedResult.content?.[0]?.text;
    return (
      <div className="p-4 bg-green-100 border border-green-400 text-green-700 rounded-md shadow-sm">
        <p className="font-semibold">地理位置已获取:</p>
        <p className="text-lg">{location || "未知位置"}</p>
      </div>
    );
  }

  if (permissionGranted === false) {
    return (
      <div className="p-4 bg-red-100 border border-red-400 text-red-700 rounded-md shadow-sm">
        <p className="font-semibold">地理位置请求已拒绝。</p>
      </div>
    );
  }

  return (
    <div className="p-6 bg-white border border-gray-300 rounded-lg shadow-md w-full">
      <h3 className="text-lg font-semibold text-gray-800 mb-2">
        请求地理位置权限
      </h3>
      <p className="text-sm text-gray-600 mb-4">{reason}</p>
      <div className="flex space-x-3">
        <button
          onClick={handleAllow}
          className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition duration-150 ease-in-out"
        >
          允许
        </button>
        <button
          onClick={handleDeny}
          className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50 transition duration-150 ease-in-out"
        >
          拒绝
        </button>
      </div>
    </div>
  );
};
