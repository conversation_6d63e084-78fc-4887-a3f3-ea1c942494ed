import React from "react";
import { ToolCallCellRendererProps } from "../../plugin-system/plugin-types";

interface WeatherToolArgs {
  city?: string;
  date?: string;
}

interface WeatherToolResult {
  content?: [{ type: "text"; text: string }];
}

/**
  * toolName = bb0i4fck_get_weather_7e03a363 
  * 工具调用参数
    "root": {
        "city":"广州市"
        "date":"today"
    }
  * 工具调用结果
    The 广州市 weather is stormy in today.
 */
export const FooWeatherToolCallCell: React.FC<ToolCallCellRendererProps> = ({
  message,
}) => {
  let weatherArgs: WeatherToolArgs = {};
  try {
    weatherArgs = JSON.parse(message.toolArgs ?? "{}") ?? {};
  } catch (e) {}

  let weatherResult: WeatherToolResult = {};
  try {
    weatherResult = JSON.parse(message.toolResult ?? "{}");
  } catch (e) {}

  const city = weatherArgs.city || "";
  const date = weatherArgs.date || "";
  const weatherDescription = weatherResult.content?.[0]?.text || "";

  // A simple placeholder for a weather icon. In a real app, you'd use an actual icon library or SVGs.
  // For example, if weatherDescription contains "stormy", you might show a storm icon.
  const getWeatherIcon = (description: string) => {
    if (description.toLowerCase().includes("stormy")) {
      return "⛈️"; // Stormy icon
    }
    if (
      description.toLowerCase().includes("sunny") ||
      description.toLowerCase().includes("clear")
    ) {
      return "☀️"; // Sunny icon
    }
    if (description.toLowerCase().includes("cloudy")) {
      return "☁️"; // Cloudy icon
    }
    if (
      description.toLowerCase().includes("rainy") ||
      description.toLowerCase().includes("rain")
    ) {
      return "🌧️"; // Rainy icon
    }
    if (description.toLowerCase().includes("snowy")) {
      return "❄️"; // Snowy icon
    }
    return "🌬️"; // Default/windy icon
  };

  return (
    <div className="p-6 bg-gradient-to-br from-blue-400 to-blue-600 text-white rounded-xl shadow-xl w-full">
      <div className="flex justify-between items-start mb-4">
        <div>
          <h2 className="text-2xl font-bold">{city}</h2>
          <p className="text-sm opacity-80">{date}</p>
        </div>
        <div className="text-5xl">{getWeatherIcon(weatherDescription)}</div>
      </div>
      <div>
        <p className="text-lg font-medium">天气状况:</p>
        <p className="text-3xl font-semibold mt-1">{weatherDescription}</p>
      </div>
    </div>
  );
};
