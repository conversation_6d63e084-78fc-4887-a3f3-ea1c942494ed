import {
  NuroAssistantMessage,
  NuroMCPClientAdapterImpl,
  NuroMessage,
  NuroTaskChecker,
  NuroToolCallMessage,
  NuroUserMessage,
} from "@byted/nurosdk-js"; // Added import
import {
  Plugin,
  PluginCapability,
  ToolCallCellRendererProps,
} from "../../plugin-system/plugin-types";
import { FooWeatherToolCallCell } from "./foo-weather-tool-call-cell";
import { FooGetLocationToolCallCell } from "./foo-get-location-tool-call-cell";
import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { z } from "zod";

class TestTaskChecker extends NuroTaskChecker {}

const FooWeatherPlugin: Plugin = {
  id: "foo_weather",
  name: "Foo Weather Plugin",
  description: "一个用于获取天气信息的插件，用于演示如何自定义工具调用界面。",
  version: "0.1.0",
  capabilities: {
    [PluginCapability.CustomTaskChecker]: () => {
      return new TestTaskChecker();
    },
    [PluginCapability.CustomMCPServer]: () => {
      const localServer = new McpServer({
        name: "local",
        version: "0.0.1",
      });
      localServer.tool(
        "get_location",
        "获取用户当前位置",
        { reason: z.string().describe("为什么需要获取地理位置，理由是什么。") },
        async () => {
          return { content: [] }; // 留空是因为我们需要通过自定义工具界面，请求用户授权获取用户的地理位置信息。
        }
      );
      return [
        {
          name: "local",
          adapter: new NuroMCPClientAdapterImpl("local", localServer),
        },
      ];
    },
    [PluginCapability.CustomToolCallCell]: (
      message: NuroToolCallMessage
    ): React.FC<ToolCallCellRendererProps> | undefined => {
      if (message.toolName.includes("get_weather")) {
        return FooWeatherToolCallCell;
      }
      if (message.toolName.includes("get_location")) {
        return FooGetLocationToolCallCell;
      }
    },
  },
};

export default FooWeatherPlugin;
