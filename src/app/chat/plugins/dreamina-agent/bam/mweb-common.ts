// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum DML_MWEB_COMMON_AIGCGenerateType {
  /** 未定义 */
  Undefined = 0,
  /** 文生图 */
  Text2Image = 1,
  /** 图片超分，也叫细节重绘（小图 -> 大图） */
  SuperResolution = 2,
  /** 用文生图方式进行微调prompt */
  FineTunePromptWithText2Image = 3,
  /** 用图片超分方式进行微调prompt */
  FineTunePromptWithSuperResolution = 4,
  /** 创意文字生成 */
  Text2CreativeTxet = 5,
  /** aigc特效 */
  SpecialEffect = 6,
  /** 图内重绘，也叫局部重绘（可以对大图进行操作，选中某些区域进行重绘） */
  InPainting = 7,
  /** 扩图 */
  OutPainting = 8,
  /** 图内消除，也叫消除笔 */
  InPaintingRemove = 9,
  /** 文生视频 */
  Text2Video = 10,
  /** 垫图 */
  Blend = 12,
  /** 无损超清 */
  NormalHd = 13,
  /** 抠图结果 */
  ImageCut = 14,
  /** 溶图 */
  ImageFusion = 15,
  /** 视频配乐 */
  VideoBGM = 16,
  /** 音视频合成 */
  AudioVideoMix = 17,
  /** 空间转向 */
  InstaDrag = 18,
  /** 图片人物生成视频 */
  Image2Avatar = 20,
  /** 视频人物生成视频 */
  Video2Avatar = 21,
  /** 文生人声音乐 */
  Text2Song = 22,
  /** 文生纯音乐 */
  Text2Instrumental = 23,
  /** 对口型 */
  LipSync = 24,
  /** 图像对口型 */
  LipSyncImage = 25,
  /** 同时局部重绘和扩图 */
  InAndOutPainting = 26,
  /** 指令编辑 */
  ByteEditPainting = 27,
  /** etta重绘 */
  EttaPainting = 28,
  /** 视频模板 */
  VideoTemplate = 29,
  /** ai特效图片产物 */
  AIEffectWorkImage = 30,
  /** ai特效视频产物 */
  AIEffectWorkVideo = 31,
  /** 视频配音效 */
  VideoAudioEffect = 32,
  /** 视频配音效合成产物 */
  VideoAudioEffectMix = 33,
}

export enum DML_MWEB_COMMON_BoxEdgeType {
  Hard = 1,
  Soft = 2,
}

export enum DML_MWEB_COMMON_CloneVoiceStatus {
  Generating = 1,
  Success = 2,
  Fail = 3,
}

export enum DML_MWEB_COMMON_CommonAssetsStatus {
  /** 删除 */
  Deleted = 22,
  /** 下架,运营平台上下线 */
  Offline = 23,
  /** 封禁,安全审核不通过 */
  Banned = 25,
  /** 内部可见配方素材初始状态 */
  Internal = 101,
  /** 上线 */
  Online = 102,
  /** 个人主页可见,仅在创作者个人主页可以看到,可收藏、使用 */
  HomepageOnline = 103,
  /** 仅在模版中可用,其他场景不做分发 */
  TemplateUse = 104,
  /** 隐私状态 */
  Private = 140,
  /** 待审 */
  Reviewing = 141,
  /** 自见,审核后自见,仅自己可见 */
  SlefView = 144,
}

export enum DML_MWEB_COMMON_GenerateAudioEffectStatus {
  Init = 0,
  Processing = 1,
  Fail = 2,
  Success = 3,
}

export enum DML_MWEB_COMMON_GenerateVideoAudioScene {
  /** 视频配乐 */
  Music = 0,
  /** 视频配音效 */
  AudioEffect = 1,
}

export enum DML_MWEB_COMMON_ImageRatioType {
  /** 图片比例1:1 */
  ImageRatioType_11 = 1,
  /** 图片比例3:4 */
  ImageRatioType_34 = 2,
  /** 图片比例16:9 */
  ImageRatioType_169 = 3,
  /** 图片比例4:3 */
  ImageRatioType_43 = 4,
  /** 图片比例9:16 */
  ImageRatioType_916 = 5,
  /** 图片比例2:3 */
  ImageRatioType_23 = 6,
  /** 图片比例3:2 */
  ImageRatioType_32 = 7,
  /** 图片比例21:9 */
  ImageRatioType_219 = 8,
}

export enum DML_MWEB_COMMON_ItemPlatform {
  /** loki 平台下发音色 */
  Loki = 1,
  /** 用户个人音色 */
  Local = 2,
  /** 模版音色 */
  Template = 3,
}

export enum DML_MWEB_COMMON_MixAudioVideoStatus {
  Init = 0,
  Processing = 1,
  Fail = 2,
  Success = 3,
}

export enum DML_MWEB_COMMON_PromptSource {
  Unknown = 0,
  /** 用户选择的标签 */
  UserTag = 1,
  /** 基于视频首帧图生成 */
  VideoFirstFrame = 2,
}

export enum DML_MWEB_COMMON_StyleType {
  /** 预设 */
  Default = 109,
  /** 自定义 */
  Customize = 214,
}

export enum DML_MWEB_COMMON_TaskPriority {
  /** 普通任务 */
  Normal = 0,
  /** 闲时任务 */
  Relax = 1,
}

/** 仅在视频首次从0到1生成的时候传 */
export enum DML_MWEB_COMMON_VideoMode {
  /** 此为旧版本 v1.0 的视频生成模式,本身无意义, 1.0模型也没有生成模式的概念 */
  V1Default = 0,
  Preview = 1,
  /** 对应 ai lab v1.2 的 default 模型 */
  Default = 2,
  /** 智创 live_photo */
  ICLivePhoto = 3,
  /** 人物生成模型 */
  Avatar = 4,
  /** 人物生成Loopy模型，对应智创 Loopy 处理器 */
  Avatar_Loopy = 5,
  /** 人物生成高品质模型 */
  Avatar_Hq = 6,
}

export interface DML_MWEB_COMMON_Ability {
  name?: string;
  text_to_image?: DML_MWEB_COMMON_Text2ImageAbility;
  face_swap?: DML_MWEB_COMMON_FaceSwapAbility;
  bg_paint?: DML_MWEB_COMMON_BgPaintAbility;
  control_net?: DML_MWEB_COMMON_ControlNetAbility;
  image_to_image?: DML_MWEB_COMMON_Image2ImageAbility;
  ip_keep?: DML_MWEB_COMMON_IpKeepAbility;
}

export interface DML_MWEB_COMMON_AIEffectInput {
  /** 特效 */
  ai_effect_id?: Int64;
  inputs?: Array<DML_MWEB_COMMON_AIEffectInputResource>;
  req_key?: string;
  generate_type?: DML_MWEB_COMMON_AIGCGenerateType;
  seed?: Int64;
}

export interface DML_MWEB_COMMON_AIEffectInputResource {
  /** 唯一标识 */
  uuid?: string;
  /** text/image/video */
  resource_type?: string;
  image?: DML_MWEB_COMMON_ImageInfo;
  text?: string;
}

export interface DML_MWEB_COMMON_AIEffectOpt {
  image?: DML_MWEB_COMMON_ImageInfo;
  req_key?: string;
  req_json?: string;
}

export interface DML_MWEB_COMMON_AIEffectUserInput {
  /** 唯一标识 */
  uuid?: string;
  /** 输入描述 */
  title?: string;
  /** 输入资源类型 */
  type?: string;
  /** 最小输入个数 image_list 才需要设置min_num与max_num，其它情况均为1 */
  min_num?: Int;
  /** 最大输入个数 image_list 才需要设置min_num与max_num，其它情况均为1 */
  max_num?: Int;
}

export interface DML_MWEB_COMMON_AIFeature {
  type?: string;
  /** 是否继承，后编辑/图生视频等场景等作品，需要保留原作品的ai feature；false: 当前item生成使用到ai_feature，true: 继承自上一个作品的ai_feature */
  is_inherit?: boolean;
  /** ai_effect的特效id: Deprecate, 使用secondKey */
  ai_effect_id?: string;
  /** 动作复刻视频模板id: Deprecate, 使用secondKey */
  video_template_item_id?: string;
  /** 二级key: 特效id, 动作复刻模板Id */
  second_key?: string;
  /** 三级key: 保留字段 */
  third_key?: string;
  /** 更多信息: json格式 */
  more_info?: string;
}

export interface DML_MWEB_COMMON_AIFeatureData {
  /** ai feature key标识 */
  features?: Array<DML_MWEB_COMMON_AIFeature>;
  /** 是否完成合并 */
  is_merged?: boolean;
}

/** ******************************* API AigcDraft&AigcRet start */
export interface DML_MWEB_COMMON_AigcDraft {
  /** x.y.z */
  version?: string;
  /** 即梦协议存储URI */
  uri?: string;
  /** 完整的即梦协议内容 */
  content?: string;
  /** 草稿更新时间 */
  update_time?: Int64;
  /** 上次全局预览的时间 */
  last_preview_time?: Int64;
  /** 产物的类型，通过不同的类型，从已有的数据结构中获取 */
  resource_type?: string;
  /** 发布后的协议存储URI */
  public_uri?: string;
  /** 记录 draft 中需要用户输入的节点信息(text/image/...) */
  variables?: Array<DML_MWEB_COMMON_AigcFlowNode>;
  /** 资源宽度，从最后一次preview中结果中保存 */
  resource_width?: Int;
  /** 资源高度，从最后一次preview中结果中保存 */
  resource_height?: Int;
  /** 存储节点列表，后续可用于筛选以及其它处理 */
  node_keys?: Array<string>;
  /** 时间预测，一期可以使用最后预览的时间做处理，单位s */
  cost?: Int;
  /** 该草稿最低使用的特性列表 */
  min_feats?: Array<string>;
}

export interface DML_MWEB_COMMON_AigcDraftNodeResource {
  /** 产物类型 text|image|video|audio|image_list */
  type?: string;
  /** 文字内容 */
  text?: string;
  /** 图片信息 */
  image?: DML_MWEB_COMMON_ImageInfo;
  /** 视频信息 */
  video?: DML_MWEB_COMMON_VideoInfo;
  /** 图片列表 */
  image_list?: Array<DML_MWEB_COMMON_ImageInfo>;
  audio?: DML_MWEB_COMMON_AudioInfo;
  /** !!!!算法返回的完整内容，如status_code -- 这里仅为特殊需要新增
其它场景请复用上述资源类型 */
  algo_datas?: Array<DML_MWEB_COMMON_AlgoData>;
  local_item_id?: Int64;
}

export interface DML_MWEB_COMMON_AigcFlowNode {
  /** uuid */
  id?: string;
  type?: string;
  text_input?: string;
  image_input?: DML_MWEB_COMMON_ImageInfo;
  generate_image_input?: DML_MWEB_COMMON_GenerateImageInput;
  generate_video_input?: DML_MWEB_COMMON_GenerateVideoInput;
  generate_text_input?: string;
  image_to_text_input?: DML_MWEB_COMMON_ImageInfo;
  face_swap_input?: DML_MWEB_COMMON_FaceSwapInput;
  style_reference_input?: DML_MWEB_COMMON_StyleReferenceInput;
  aigc_mode?: string;
  generate_audio_input?: DML_MWEB_COMMON_GenerateAudioInput;
  video_input?: DML_MWEB_COMMON_VideoInfo;
  /** 旧生图链路迁移协议至即梦协议，新的生成能力请新定义input，不要在这里增加 */
  m_generate_image_input?: DML_MWEB_COMMON_MGenerateImageInput;
  m_generate_video_input?: DML_MWEB_COMMON_MGenerateVideoInput;
  /** AI特效的输入 */
  ai_effect_input?: DML_MWEB_COMMON_AIEffectInput;
  /** 视频配音效 */
  generate_video_audio_effect_input?: DML_MWEB_COMMON_GenerateVideoAudioEffect;
  /** 该草稿最低使用的特性列表 */
  min_feats?: Array<string>;
  /** 节点描述，如果是输入节点，需要将描述信息带上来 */
  description?: string;
}

export interface DML_MWEB_COMMON_AigcRet {
  /** draft id */
  draft_id?: string;
  /** task id */
  task_id?: string;
  /** task 状态 */
  status?: Int;
  /** 防止 draft_id 中草稿更新 */
  draft_uri?: string;
  /** 记录用户的输入(text/image/...), 主要用于复现内容,先使用list<AigcFlowNode>的序列化内容 */
  variables?: Array<DML_MWEB_COMMON_AigcFlowNode>;
  /** text => 存储到description
image => 存储到LargeImage
imageList => 待讨论？
video => 存储到Video
产物数据 */
  resource?: DML_MWEB_COMMON_AigcDraftNodeResource;
  /** 如果出错了，这里承载对应的状态码 */
  status_code?: Int;
}

export interface DML_MWEB_COMMON_AlgoData {
  image_info?: DML_MWEB_COMMON_ImageInfo;
  status_code?: Int;
  req_json?: string;
  resp_json?: string;
  /** 生成时候需要传递的不重要参数放这里 */
  ext?: DML_MWEB_COMMON_DataExt;
}

export interface DML_MWEB_COMMON_AudioInfo {
  /** vid */
  vid?: string;
  /** 时长 */
  duration?: Int;
  /** 音频链接 */
  audio_url?: string;
  link?: string;
  /** 音乐格式"m4a", "wav", "mp3", "aac" */
  format?: string;
  /** 音乐尺寸，单位：byte */
  size?: Int;
  /** 音乐加密密钥 */
  encryption_key?: string;
  title?: string;
  /** 歌词 */
  lyrics?: string;
  /** 字幕 */
  captions?: Array<DML_MWEB_COMMON_Utterances>;
  /** 时长 毫秒 */
  duration_ms?: Int64;
  url?: string;
}

/** AudioTag imc配置 https://imc.bytedance.net/resource-bit/standard-resource-bits/resource?curAppId=513695&bId=1521&beeHref=https%3A%2F%2Fbee.bytedance.net%2F1521%2Fcombine-imc%2F16367%2Fdata */
export interface DML_MWEB_COMMON_AudioTag {
  /** key用于索引tag，以便编辑时还原参数 对应imc-tag_key */
  key?: string;
  /** name用于存储tag的字面值，以便服务端拼接Prompt 对应imc-tag_text */
  name?: string;
  /** tab展示标签 */
  type?: string;
}

export interface DML_MWEB_COMMON_AudioVideoMixConf {
  enable?: boolean;
  /** 用于音视频合成的视频ItemID */
  video_item_id?: Int64;
  /** 用于音视频合成的音频VID */
  audio_vid?: string;
  /** 音视频合成任务的参数 */
  param_json?: string;
  /** 音视频合成场景，0：视频配乐结果的合成，1：视频配音效结果的合成 */
  scene?: DML_MWEB_COMMON_GenerateVideoAudioScene;
}

export interface DML_MWEB_COMMON_BgPaintAbility {
  image?: DML_MWEB_COMMON_ImageInfo;
  mask?: DML_MWEB_COMMON_ImageInfo;
  /** 用户上传原始图片 */
  origin_image?: DML_MWEB_COMMON_ImageInfo;
  origin_mask?: DML_MWEB_COMMON_ImageInfo;
}

export interface DML_MWEB_COMMON_BlendPromptPlaceHolderInfo {
  /** 能力列表的下标，表示该图片是被用在哪个能力上 */
  ability_index?: Int;
}

export interface DML_MWEB_COMMON_BoxEdges {
  /** 长度为2, 为矩形的左上,右下顶点坐标, 其中竖轴为 y 轴
矩形平行于坐标轴时,矩形的两个对角顶点(vertex) 坐标唯一确定一个矩形的四条边 (edge) */
  vertexs?: Array<Array<Double>>;
  edge_type?: DML_MWEB_COMMON_BoxEdgeType;
}

export interface DML_MWEB_COMMON_Boximator {
  enable?: boolean;
  /** 可以放置多个 box, 每个 box 有起始位置 */
  boxes?: Array<DML_MWEB_COMMON_BoximatorBox>;
  boximator_image?: DML_MWEB_COMMON_ImageInfo;
  /** boximator 还原需要较多信息,fe 可以在这个字段中存储相关还原需要的信息 */
  draft?: string;
  /** draft对应的文件，后续boximator都上传文件uri */
  draft_file?: DML_MWEB_COMMON_FileInfo;
}

export interface DML_MWEB_COMMON_BoximatorBox {
  /** 表示 box 的颜色 16进制的 rgb, #ff0000 */
  color?: string;
  /** 一个 BoximatorBox 表示一个矩形的运动轨迹, 可以输入轨迹上的多个 box 以便算法做更精细的生成,
start_box, end_box 必传, 中间的 box 暂时不支持, 当前的语义 boxes[0] 是 start_box, boxes[len(boxes)-1] 是 end_box */
  boxes?: Array<DML_MWEB_COMMON_BoxEdges>;
  /** motion_path box 运动轨迹, 直线部分可以减少采样,曲线部分可以加多采样 */
  motion_path?: Array<Array<Double>>;
  /** prompt 表示主体的简略描述, 比如宇航员,狮子 */
  prompt?: string;
}

export interface DML_MWEB_COMMON_CanvasDraftImage {
  workspace_id?: string;
  asset_id?: string;
}

/** 客户端追踪数据，服务端保存但不使用 */
export interface DML_MWEB_COMMON_ClientTraceData {
  impression_id?: string;
  activity_id?: string;
  hashtag_id?: string;
  profile_id?: string;
  is_regenerate?: boolean;
}

export interface DML_MWEB_COMMON_CommerceReqInfo {
  resource_sub_type?: string;
  resource_id_type?: string;
  resource_id?: string;
  /** 权益类型 */
  benefit_type?: string;
  /** 消耗权益计量，如视频s数 */
  amount?: Int;
  /** 在单批多明细时使用，有传则调用商业化AddCreditConsumptionV2 */
  credit_items?: Array<DML_MWEB_COMMON_CreditItem>;
}

export interface DML_MWEB_COMMON_CommonAssets {
  /** 资产类型 */
  asset_type?: string;
  /** 资产代码 */
  asset_code?: string;
  /** 资产状态 */
  status?: DML_MWEB_COMMON_CommonAssetsStatus;
  /** 资产具体内容 */
  content?: DML_MWEB_COMMON_CommonAssetsContent;
  /** 原始itemid */
  item_id?: Int64;
  /** 原始userid */
  user_id?: Int64;
  /** 创建时间 */
  create_time?: Int64;
  /** 更新时间 */
  update_time?: Int64;
}

export interface DML_MWEB_COMMON_CommonAssetsContent {
  /** 资产引用的原始模板图片信息 */
  refer_image_list?: Array<DML_MWEB_COMMON_CommonAssetsReferImage>;
  /** 基于此资产码生成的item_ids */
  generate_item_ids?: Array<Int64>;
}

export interface DML_MWEB_COMMON_CommonAssetsReferImage {
  /** 引用图片 */
  image?: DML_MWEB_COMMON_ImageInfo;
  /** 引用图的风格系数 */
  style_weight?: Double;
}

export interface DML_MWEB_COMMON_ControlNet {
  name?: string;
  /** 取值(0.0, 1.0] */
  strength?: Double;
  canny?: DML_MWEB_COMMON_ControlNetCanny;
  depth?: DML_MWEB_COMMON_ControlNetDepth;
  pose?: DML_MWEB_COMMON_ControlNetPose;
  bg_paint?: DML_MWEB_COMMON_ControlNetBgPaint;
  style_reference?: DML_MWEB_COMMON_ControlStyleReference;
  image_index?: Int;
  /** bgpaint能力的mask图下标 */
  mask_index?: Int;
}

export interface DML_MWEB_COMMON_ControlNetAbility {
  control_net_list?: Array<DML_MWEB_COMMON_ControlNet>;
}

export interface DML_MWEB_COMMON_ControlNetBgPaint {
  image?: DML_MWEB_COMMON_ImageInfo;
  mask?: DML_MWEB_COMMON_ImageInfo;
  origin_image?: DML_MWEB_COMMON_ImageInfo;
  origin_mask?: DML_MWEB_COMMON_ImageInfo;
}

export interface DML_MWEB_COMMON_ControlNetCanny {
  image?: DML_MWEB_COMMON_ImageInfo;
  origin_image?: DML_MWEB_COMMON_ImageInfo;
  preview_image?: DML_MWEB_COMMON_ImageInfo;
}

export interface DML_MWEB_COMMON_ControlNetDepth {
  image?: DML_MWEB_COMMON_ImageInfo;
  origin_image?: DML_MWEB_COMMON_ImageInfo;
  preview_image?: DML_MWEB_COMMON_ImageInfo;
}

export interface DML_MWEB_COMMON_ControlNetPose {
  image?: DML_MWEB_COMMON_ImageInfo;
  origin_image?: DML_MWEB_COMMON_ImageInfo;
  preview_image?: DML_MWEB_COMMON_ImageInfo;
}

export interface DML_MWEB_COMMON_ControlStyleReference {
  /** 用户上传图 */
  image?: DML_MWEB_COMMON_ImageInfo;
}

/** ********************** */
export interface DML_MWEB_COMMON_CreditItem {
  /** 权益类型 */
  benefit_type?: string;
  /** 消耗的权益量 */
  amount?: Int;
}

export interface DML_MWEB_COMMON_DataExt {
  prompt_fallback?: boolean;
  ab_tags?: Array<string>;
  actual_request_key?: string;
  is_antidirt_rewrite?: boolean;
}

export interface DML_MWEB_COMMON_EverPhoto {
  asset_id?: string;
  workspace_id?: string;
}

export interface DML_MWEB_COMMON_ExtendConf {
  enable?: boolean;
  extend_duration_ms?: Int;
  extend_fps?: Int;
  /** 延长部分的 prompt */
  extend_prompt?: string;
}

export interface DML_MWEB_COMMON_ExtendVideoOpt {
  video?: DML_MWEB_COMMON_VideoInfo;
  extend_duration_ms?: Int;
  extend_fps?: Int;
  /** 延长部分的 prompt */
  extend_prompt?: string;
}

export interface DML_MWEB_COMMON_Face {
  /** 用户上传的人脸数据 */
  image?: DML_MWEB_COMMON_ImageInfo;
  /** face_recognize 存放该图片识别到的所有人脸数据 */
  face_recognize?: Array<DML_MWEB_COMMON_FaceRecognize>;
}

export interface DML_MWEB_COMMON_FaceRecognize {
  /** keypoint 人脸关键点 */
  keypoint?: Array<Int>;
  /** 人脸位置,坐标为[left_x, top_y, width, height], (left_x, top_y)为人脸左上角坐标，width，height为人脸的宽高 */
  face_rect?: Array<Int>;
  /** 代表用户是否选择了该人脸关键点 */
  is_selected?: boolean;
}

export interface DML_MWEB_COMMON_FaceSwapAbility {
  /** 人脸图片以及识别到的人脸关键点数据 */
  face?: Array<DML_MWEB_COMMON_Face>;
}

/** 用target_face的脸替换origin_face */
export interface DML_MWEB_COMMON_FaceSwapInput {
  origin_face?: DML_MWEB_COMMON_ImageInfo;
  target_face?: DML_MWEB_COMMON_ImageInfo;
}

export interface DML_MWEB_COMMON_FileInfo {
  file_uri?: string;
  file_url?: string;
  base64?: string;
}

export interface DML_MWEB_COMMON_FusionParams {
  /** 背景图 */
  bg_image?: DML_MWEB_COMMON_ImageInfo;
  /** 前景图 */
  fg_images?: Array<DML_MWEB_COMMON_ImageInfo>;
  /** 强度 */
  strength?: Double;
}

/** 生图关键参数 */
export interface DML_MWEB_COMMON_GenCoreParam {
  /** 模型的req_key */
  model?: string;
  /** 用户输入文本 */
  prompt?: string;
  /** 屏蔽词 */
  negative_prompt?: string;
  sample_steps?: Int64;
  /** [1, 21亿]
随机种子，-1为不随机种子；其他为指定随机种子,默认值：-1。 */
  seed?: Int64;
  /** 采样强度 */
  sample_strength?: Double;
  /** 图片比例，如果比例是无效枚举值，则取large_image_info的图片信息 */
  image_ratio?: DML_MWEB_COMMON_ImageRatioType;
  /** 指定结果图尺寸，优先级比image_ratio高 */
  large_image_info?: DML_MWEB_COMMON_ReqImageInfo;
  strength?: Double;
}

export interface DML_MWEB_COMMON_GenerateAudioInput {
  type?: string;
  /** 提示词 */
  prompt?: string;
  /** 配乐时长 单位为秒 */
  duration?: Int;
  /** 生成音频时使用的标签。 */
  tags?: Array<DML_MWEB_COMMON_AudioTag>;
  /** 歌名可选 */
  title?: string;
  /** 歌词可选 vocal场景有效 */
  lyrics?: string;
}

export interface DML_MWEB_COMMON_GenerateDebugInfo {
  logid?: string;
  env?: string;
}

export interface DML_MWEB_COMMON_GenerateImageInput {
  prompt?: string;
  /** 指定结果图比例：
1:1 => 1024*1024
2:3、3:2 => 1024*682
3:4、4:3 => 1024*768
9:16、16:9 => 1024*576 */
  image_ratio?: string;
  /** 生图数量 默认1张 */
  gen_count?: Int;
  /** 最少成功张数 默认1张 */
  min_count?: Int;
  /** 如果设定了图片尺寸，上面的比例将失效 */
  width?: Int64;
  height?: Int64;
  ability_list?: Array<DML_MWEB_COMMON_Ability>;
}

export interface DML_MWEB_COMMON_GenerateVideoAudioEffect {
  /** 使用哪个history配音效 */
  origin_history_id?: Int64;
  /** 使用哪个item配音效 */
  origin_item_id?: Int64;
}

export interface DML_MWEB_COMMON_GenerateVideoInput {
  name?: string;
  generate_video?: DML_MWEB_COMMON_GenerateVideoOpt;
  extend_video?: DML_MWEB_COMMON_ExtendVideoOpt;
  super_resolution?: DML_MWEB_COMMON_SuperResolutionOpt;
  insert_frame?: DML_MWEB_COMMON_InsertFrameOpt;
  lip_sync?: DML_MWEB_COMMON_LipSyncOpt;
  lab_sr?: DML_MWEB_COMMON_LabSrOpt;
  livephoto?: DML_MWEB_COMMON_LivePhotoOpt;
  lip_sync_image?: DML_MWEB_COMMON_LipSyncImageOpt;
  ai_effect?: DML_MWEB_COMMON_AIEffectOpt;
}

export interface DML_MWEB_COMMON_GenerateVideoOpt {
  /** 提示词 */
  prompt?: string;
  /** 首帧图 */
  first_frame_image?: DML_MWEB_COMMON_ImageInfo;
  /** 尾帧图 */
  end_frame_image?: DML_MWEB_COMMON_ImageInfo;
  /** ai视频镜头运动方式 */
  lens_motion_type?: string;
  /** ai视频运动强度 */
  motion_speed?: string;
  /** ai视频尾帧控制 */
  ending_control?: string;
  /** 生成视频宽高比，对齐下游算法输入类型，使用字符串存储，例如"16:9"。如果提供了首帧图，则取首帧图宽高比 */
  video_aspect_ratio?: string;
  /** uint32 类型的数字 */
  seed?: Int64;
  /** 任务优先级，分为正常任务和闲时任务 */
  priority?: DML_MWEB_COMMON_TaskPriority;
  fps?: Int;
  /** 视频生成时表示生成多长的视频 */
  duration_ms?: Int;
  /** 运镜运动幅度,仅在 AILABV2_DEFAULT/AILABV2_BASE 中有效 0,1,2 */
  camera_strength?: string;
  /** 首次生成时传有效, 标识是生成预览还是普通视频 */
  video_mode?: DML_MWEB_COMMON_VideoMode;
}

export interface DML_MWEB_COMMON_I2VOpt {
  realman_avatar?: DML_MWEB_COMMON_RealmanAvatar;
  live_photo?: DML_MWEB_COMMON_Livephoto;
}

export interface DML_MWEB_COMMON_IdInfo {
  /** 音色来源 */
  item_platform: DML_MWEB_COMMON_ItemPlatform;
  /** Loki 对应用 resource_id, Local 对应用 local_item_id, Template 对应用 published_item_id */
  id?: string;
}

export interface DML_MWEB_COMMON_Image2ImageAbility {
  strength?: Double;
  image?: DML_MWEB_COMMON_ImageInfo;
}

/** 尽量与 api_common_struct.ImageInfo 对齐 */
export interface DML_MWEB_COMMON_ImageInfo {
  /** 作为请求参数，会优先取image_uri参数 */
  image_uri?: string;
  /** 无论前端请求使用image_uri还是用云相册资产ID，
业务都会转存和下发image_uri和image_url */
  image_url?: string;
  width?: Int;
  height?: Int;
  format?: string;
  /** 云相册信息，当请求进来时会对这个内部做打包操作，转换成image_uri，后续的处理过程无需考虑ever_photo的存在 */
  ever_photo?: DML_MWEB_COMMON_EverPhoto;
  /** 生图图片信息，当请求进来时会对这个内部做打包操作，转换成image_uri，后续的处理过程无需考虑local_image的存在 */
  aigc_image?: DML_MWEB_COMMON_MAigcImage;
  /** 多尺寸图片，不一定所有图片都有这个字段 */
  cover_url_map?: Record<string, string>;
  /** 原图大小的url，如果是AIGC的图片，会带上水印下发 */
  origin_url?: string;
  /** 图片标题信息 作为请求参数 */
  title?: string;
  /** 图片的一些link信息 */
  link?: string;
  /** 是否是动图 */
  is_animated?: boolean;
}

export interface DML_MWEB_COMMON_ImageScene {
  /** 场景值 不同的场景对应不同的模版 */
  scene?: string;
  height?: Int;
  width?: Int;
  format?: string;
  /** 唯一标识，会用来做返回的 map key */
  uniq_key?: string;
}

export interface DML_MWEB_COMMON_InsertFrameConf {
  enable?: boolean;
  /** 目标帧率 */
  target_fps?: Int;
  /** 原帧率 */
  origin_fps?: Int;
  /** 视频时长,单位 ms */
  duration_ms?: Int;
}

export interface DML_MWEB_COMMON_InsertFrameOpt {
  video?: DML_MWEB_COMMON_VideoInfo;
  /** 目标帧率 */
  target_fps?: Int;
  /** 原帧率 */
  origin_fps?: Int;
  /** 视频时长,单位 ms */
  duration_ms?: Int;
}

export interface DML_MWEB_COMMON_InstaDragParams {
  mask_uri?: string;
  mask_url?: string;
  points?: Array<DML_MWEB_COMMON_InstaDragPoint>;
  origin_item_id?: string;
}

export interface DML_MWEB_COMMON_InstaDragPoint {
  start_x?: Int;
  start_y?: Int;
  end_x?: Int;
  end_y?: Int;
}

export interface DML_MWEB_COMMON_IpKeepAbility {
  /** 角色描述 */
  description?: string;
  /** 参考图主体的权重，越大生成结果和参考图中主体的相似度越高,[0， 1] */
  ref_ip_weight?: Double;
  /** 参考图人脸的权重，越大生成结果和参考图中人脸的相似度越高,[0， 1] */
  ref_id_weight?: Double;
  /** 参考图 */
  image?: DML_MWEB_COMMON_ImageInfo;
  /** 引用角色的id */
  character_id?: Int64;
  /** 引用角色的名称 */
  character_name?: string;
}

export interface DML_MWEB_COMMON_LabSrConf {
  enable?: boolean;
  sr_fps?: Int;
  /** 视频生成时表示生成多长的视频 */
  sr_duration_ms?: Int;
}

/** AILab SR, 只能针对 AILab 生成的视频 */
export interface DML_MWEB_COMMON_LabSrOpt {
  video?: DML_MWEB_COMMON_VideoInfo;
  sr_fps?: Int;
  /** 视频生成时表示生成多长的视频 */
  sr_duration_ms?: Int;
}

export interface DML_MWEB_COMMON_LipSyncAudioInfo {
  url?: string;
  file_id?: string;
  duration?: Double;
  format?: string;
  file_hash?: string;
  vid?: string;
}

export interface DML_MWEB_COMMON_LipSyncConf {
  enable?: boolean;
  audio_vid?: string;
  /** 对口型音频信息 */
  origin_audio?: DML_MWEB_COMMON_LipSyncAudioInfo;
  /** 对口型视频信息 */
  origin_video?: DML_MWEB_COMMON_VideoInfo;
  /** 前端记录的 tts 信息，做同款用，其中 source_type 为 text-to-speech 代表tts生成，服务端会根据此判断是否将音频送审 */
  tts_info?: string;
  /** 音色信息：生成时由前端传入；如果一开始是本地音色，发布的同款会更新为发布后的音色信息 */
  voice_info?: DML_MWEB_COMMON_VoiceInfo;
  /** 记录音色迁移前的原始音频 */
  audio_before_conversion?: DML_MWEB_COMMON_LipSyncAudioInfo;
}

export interface DML_MWEB_COMMON_LipSyncImageOpt {
  /** 图片对口型 */
  image?: DML_MWEB_COMMON_ImageInfo;
  /** 音频 */
  audio?: DML_MWEB_COMMON_AudioInfo;
  /** 标准模型形象id */
  resource_id_std?: string;
  /** loopy模型形象id */
  resource_id_loopy?: string;
  tts_content?: string;
  /** 从视频中提取音频 */
  audio_video?: DML_MWEB_COMMON_VideoInfo;
  /** 不传默认是loopy，传standard是标准模式 */
  mode?: string;
  /** 根据该数据字段判断是否需要超分，width 和 height必传，uri 可选、目前不会在服务端使用 */
  face_image?: DML_MWEB_COMMON_ImageInfo;
  seed?: string;
}

export interface DML_MWEB_COMMON_LipSyncOpt {
  /** 视频对口型，对口型生成结果不会保留传入视频附带的音轨 */
  video?: DML_MWEB_COMMON_VideoInfo;
  /** 驱动对口型的音频 */
  audio?: DML_MWEB_COMMON_AudioInfo;
  tts_content?: string;
  /** 需要合成的音效音频，基于音效生成结果对口型时，把音效的信息放在这个字段带过来 */
  video_effect_audio?: DML_MWEB_COMMON_AudioInfo;
}

export interface DML_MWEB_COMMON_Livephoto {
  /** 传true代表端上已经做过人脸检测了，服务端无需再做人脸检测 */
  face_checked?: boolean;
}

export interface DML_MWEB_COMMON_LivePhotoOpt {
  /** 提示词 */
  prompt?: string;
  /** 首帧图 */
  first_frame_image?: DML_MWEB_COMMON_ImageInfo;
  seed?: Int64;
  resolution?: Int64;
}

export interface DML_MWEB_COMMON_MAigcImage {
  item_id?: Int64;
}

export interface DML_MWEB_COMMON_MAigcVideo {
  item_id?: Int64;
}

export interface DML_MWEB_COMMON_MBlendReq {
  core_param?: DML_MWEB_COMMON_GenCoreParam;
  ability_list?: Array<DML_MWEB_COMMON_MReqBlendAbility>;
  /** 图片最长边 */
  image_size?: Int;
  history_option?: DML_MWEB_COMMON_MHistoryOption;
  /** prompt引用的图片具体属于哪个能力以及图片列表具体下标 */
  prompt_placeholder_info_list?: Array<DML_MWEB_COMMON_BlendPromptPlaceHolderInfo>;
  postedit_param?: DML_MWEB_COMMON_PostEditParam;
}

export interface DML_MWEB_COMMON_MGenerateImageInput {
  api?: string;
  generate_req?: DML_MWEB_COMMON_MGenerateReq;
  blend_req?: DML_MWEB_COMMON_MBlendReq;
  mix_blend_req?: DML_MWEB_COMMON_MMixBlendReq;
  painting_req?: DML_MWEB_COMMON_MPaintingReq;
  super_resolution_req?: DML_MWEB_COMMON_MSuperResolutionReq;
  normal_hd_req?: DML_MWEB_COMMON_MNormalHdReq;
}

export interface DML_MWEB_COMMON_MGenerateReq {
  core_param?: DML_MWEB_COMMON_GenCoreParam;
  /** 生成图像的步数,默认值：20，取值范围[1-50] */
  history_option?: DML_MWEB_COMMON_MHistoryOption;
}

/** ******************************* 图片节点定义 end GenerateImageInput
 ******************************* 视频节点定义 start GenerateVideoInput */
export interface DML_MWEB_COMMON_MGenerateVideoInput {
  input?: DML_MWEB_COMMON_Text2VideoParams;
  /** 场景信息，文生视频默认为空，对口型视频为 lip_sync */
  scene?: string;
}

/** 历史记录保存、查询相关参数 */
export interface DML_MWEB_COMMON_MHistoryOption {
  /** 当AIGCMode为story时，需要传入story_id以便隔离不同story下的历史记录 */
  story_id?: Int64;
  /** 当AIGCMode为character时，需要传入character_id以便隔离不同character下的历史记录 */
  character_id?: Int64;
}

export interface DML_MWEB_COMMON_MMixBlendReq {
  core_param?: DML_MWEB_COMMON_GenCoreParam;
  ability_list?: Array<Array<DML_MWEB_COMMON_MReqBlendAbility>>;
  /** len(ability_list)=0为默认模式, 默认使用ablility_list=[canny, depth, image2image, pose] */
  is_deafult?: boolean;
  /** 图片列表 */
  image_uri_list?: Array<string>;
}

export interface DML_MWEB_COMMON_MNormalHdReq {
  postedit_param?: DML_MWEB_COMMON_PostEditParam;
}

export interface DML_MWEB_COMMON_ModelOption {
  /** 对于t2v场景，先做t2i，再走i2v链路。启用该特性相当于将t2v的流量全部转接到i2v上。 */
  enable_t2i2v?: boolean;
  /** 自定义t2v的reqKey */
  t2i_req_key?: string;
}

export interface DML_MWEB_COMMON_MPaintingReq {
  core_param?: DML_MWEB_COMMON_GenCoreParam;
  postedit_param?: DML_MWEB_COMMON_PostEditParam;
  mask_uri?: string;
  /** outpaint扩图参数 */
  up_scale?: DML_MWEB_COMMON_ReqOutPaintingUpScale;
  /** 除了itemId, canvas_draft_image的方式，额外新增原始uri的方式上传图 */
  origin_uri?: string;
}

export interface DML_MWEB_COMMON_MReqBlendAbility {
  name?: string;
  /** deprecate */
  image_uri_list?: Array<string>;
  /** 图片信息 */
  image_list?: Array<DML_MWEB_COMMON_ImageInfo>;
  face_recognize_list?: Array<Array<DML_MWEB_COMMON_FaceRecognize>>;
  image_weight_list?: Array<Double>;
  control_net_list?: Array<DML_MWEB_COMMON_ControlNet>;
  extra?: string;
  strength?: Double;
  /** bgpaint能力的mask图下标 -- 重复定义，迁移后看情况删除 */
  mask_index?: Int;
  /** 角色引用生图参数 */
  ip_keep_list?: Array<DML_MWEB_COMMON_IpKeepAbility>;
  /** 风格参考参数 */
  style_reference?: DML_MWEB_COMMON_MStyleReference;
  /** 资产库 */
  common_asset?: DML_MWEB_COMMON_CommonAssets;
}

export interface DML_MWEB_COMMON_MStyleReference {
  /** 风格迁移强度,前端默认值为0.4,范围待定 */
  style_weight?: Double;
  /** 被参考图，会下发多尺寸图片 */
  image?: DML_MWEB_COMMON_ImageInfo;
  /** 目的为了让前端的生图记录可以包含vimo的存储信息 并能通过拉取信息进行面板的恢复
风格存储vimo的item_id */
  style_item_id?: string;
  /** 风格存储vimo的item_title */
  style_title?: string;
  style_title_starling_key?: string;
  /** 风格类型 */
  style_type?: DML_MWEB_COMMON_StyleType;
}

export interface DML_MWEB_COMMON_MSuperResolutionReq {
  /** 超分填2，超分微调prompt填4 */
  core_param?: DML_MWEB_COMMON_GenCoreParam;
  postedit_param?: DML_MWEB_COMMON_PostEditParam;
  canvas_layer_id?: Int64;
}

/** 后编辑关键参数 */
export interface DML_MWEB_COMMON_PostEditParam {
  /** 超分填2，超分微调prompt填4 */
  generate_type?: DML_MWEB_COMMON_AIGCGenerateType;
  item_id?: Int64;
  /** 原图的历史记录id */
  origin_history_id?: Int64;
  history_option?: DML_MWEB_COMMON_MHistoryOption;
  /** 画布上传图片 */
  canvas_draft_image?: DML_MWEB_COMMON_CanvasDraftImage;
}

export interface DML_MWEB_COMMON_PublishOpt {
  /** 是否公开个人音色 */
  public_clone_voice?: boolean;
  /** 是否公开对口型音频 */
  public_user_audio?: boolean;
  /** 是否公开对口型视频 */
  public_user_video?: boolean;
}

export interface DML_MWEB_COMMON_RealmanAvatar {
  enable?: boolean;
  resource_id_std?: string;
  resource_id_loopy?: string;
  origin_image?: DML_MWEB_COMMON_ImageInfo;
  origin_audio?: DML_MWEB_COMMON_LipSyncAudioInfo;
  /** 前端记录的 tts 信息，做同款用，其中 source_type 为 text-to-speech 代表tts生成，服务端会根据此判断是否将音频送审 */
  tts_info?: string;
  /** 音色信息：生成时由前端传入；如果一开始是本地音色，发布的同款会更新为发布后的音色信息 */
  voice_info?: DML_MWEB_COMMON_VoiceInfo;
  /** 记录音色迁移前的原始音频 */
  audio_before_conversion?: DML_MWEB_COMMON_LipSyncAudioInfo;
}

export interface DML_MWEB_COMMON_RefHistoryInfo {
  /** history_id */
  history_record_id?: string;
  item_id?: string;
  /** 图片信息，上传的时候上传uri，下发的时候服务端实时下发链接 */
  image_info?: DML_MWEB_COMMON_ImageInfo;
  prompt?: string;
}

export interface DML_MWEB_COMMON_ReqImageInfo {
  height?: Int;
  width?: Int;
  format?: string;
  image_scene_list?: Array<DML_MWEB_COMMON_ImageScene>;
  /** 过期时间 */
  expire?: Int64;
}

export interface DML_MWEB_COMMON_ReqOutPaintingUpScale {
  /** 图片上边扩增区域对原图高的占比，无扩增表示为0, 取值[0,x) */
  top?: Double;
  /** 图片下边扩增区域对原图高的占比，无扩增表示为0, 取值[0,x) */
  bottom?: Double;
  /** 图片左边扩增区域对原图宽的占比，无扩增表示为0, 取值[0,x) */
  left?: Double;
  /** 图片右边扩增区域对原图宽的占比，无扩增表示为0, 取值[0,x) */
  right?: Double;
  /** 图片最长边 */
  max_size?: Int64;
  /** 图片比例 */
  image_ratio?: DML_MWEB_COMMON_ImageRatioType;
}

export interface DML_MWEB_COMMON_SceneVideoUrl {
  download_url?: string;
}

/** 参考target_image的风格，迁移至origin_image */
export interface DML_MWEB_COMMON_StyleReferenceInput {
  origin_face?: DML_MWEB_COMMON_ImageInfo;
  target_face?: DML_MWEB_COMMON_ImageInfo;
}

export interface DML_MWEB_COMMON_SuperResolutionConf {
  enable?: boolean;
  /** 目标宽 */
  target_width?: Int64;
  /** 目标高 */
  target_height?: Int64;
  origin_width?: Int64;
  origin_heigth?: Int64;
}

/** 智创的SR */
export interface DML_MWEB_COMMON_SuperResolutionOpt {
  video?: DML_MWEB_COMMON_VideoInfo;
  /** 目标宽 */
  target_width?: Int;
  /** 目标高 */
  target_height?: Int;
}

export interface DML_MWEB_COMMON_TaskRet {
  title?: string;
  image_info?: DML_MWEB_COMMON_ImageInfo;
}

export interface DML_MWEB_COMMON_Text2ImageAbility {}

export interface DML_MWEB_COMMON_Text2VideoParams {
  video_gen_inputs?: Array<DML_MWEB_COMMON_VideoGenInput>;
  /** 生成视频宽高比，对齐下游算法输入类型，使用字符串存储，例如"16:9"。如果提供了首帧图，则取首帧图宽高比 */
  video_aspect_ratio?: string;
  /** uint32 类型的数字，[1, 21亿] */
  seed?: Int64;
  /** 任务优先级 */
  priority?: DML_MWEB_COMMON_TaskPriority;
  /** model */
  model_req_key?: string;
  /** model option，与model_req_key相关 */
  model_option?: DML_MWEB_COMMON_ModelOption;
}

export interface DML_MWEB_COMMON_Utterances {
  /** 开始时间 ms */
  start_time?: Int;
  /** 结束时间 ms */
  end_time?: Int;
  /** 字幕文本 */
  text?: string;
  /** 字幕文本每个字的开始结束时间 words无下一层级 */
  words?: Array<DML_MWEB_COMMON_Word>;
}

export interface DML_MWEB_COMMON_V2VOpt {
  /** 视频延长3s */
  extend?: DML_MWEB_COMMON_ExtendConf;
  /** 插帧 */
  insert_frame?: DML_MWEB_COMMON_InsertFrameConf;
  /** 超分 */
  super_resolution?: DML_MWEB_COMMON_SuperResolutionConf;
  /** AIGC 生成视频对口型 */
  lip_sync?: DML_MWEB_COMMON_LipSyncConf;
  lab_sr?: DML_MWEB_COMMON_LabSrConf;
  /** 音视频合成 */
  av_mix?: DML_MWEB_COMMON_AudioVideoMixConf;
  /** 用户上传视频对口型 */
  lip_sync_user_video?: DML_MWEB_COMMON_LipSyncConf;
  /** 视频模板 */
  video_template?: DML_MWEB_COMMON_VideoTemplateConf;
}

export interface DML_MWEB_COMMON_VideoAudioEffectInput {
  /** 原视频的HistoryID */
  origin_history_id?: string;
  /** 原视频的ItemID */
  origin_item_id?: string;
}

export interface DML_MWEB_COMMON_VideoBGMParams {
  /** 原视频的HistoryID */
  origin_history_id?: string;
  /** 原视频的ItemID */
  origin_item_id?: string;
  /** BGM时长，单位为秒 */
  duration?: Int64;
  /** 用户选择的tags */
  tags?: Array<DML_MWEB_COMMON_AudioTag>;
  /** 用户输入的Prompt */
  prompt?: string;
  /** Prompt生成时使用的视频首帧图（如有） */
  video_first_frame?: DML_MWEB_COMMON_ImageInfo;
  prompt_source?: DML_MWEB_COMMON_PromptSource;
  /** 从tags、首帧图、输入的prompt衍生出来的Prompt */
  derived_prompt?: string;
  /** Instrumental模型版本、Caption模型版本、豆包模型版本等信息 */
  extra?: string;
}

export interface DML_MWEB_COMMON_VideoGenInput {
  prompt?: string;
  /** 首帧图片 */
  first_frame_image?: DML_MWEB_COMMON_ImageInfo;
  /** 尾帧图片 */
  end_frame_image?: DML_MWEB_COMMON_ImageInfo;
  /** ai视频镜头运动方式 */
  lens_motion_type?: string;
  /** ai视频运动强度 */
  motion_speed?: string;
  /** 当需要延长某个视频时, 可以传这个字段 */
  vid?: string;
  /** ai视频尾帧控制 */
  ending_control?: string;
  /** 当需要延长某个视频时, 可以传这个字段 */
  pre_task_id?: Int64;
  /** 音频的vid */
  audio_vid?: string;
  v2v_opt?: DML_MWEB_COMMON_V2VOpt;
  /** 首次生成时传有效, 标识是生成预览还是普通视频 */
  video_mode?: DML_MWEB_COMMON_VideoMode;
  fps?: Int;
  /** 视频生成时表示生成多长的视频 */
  duration_ms?: Int;
  /** 运镜运动幅度,仅在 AILABV2_DEFAULT/AILABV2_BASE 中有效 0,1,2 */
  camera_strength?: string;
  boximator?: DML_MWEB_COMMON_Boximator;
  /** 使用模板做同款的时候，用到的模板id */
  template_id?: Int64;
  i2v_opt?: DML_MWEB_COMMON_I2VOpt;
  /** 引用的history信息，服务端做透传，下发的时候会填充图片的信息 */
  ref_history_info?: DML_MWEB_COMMON_RefHistoryInfo;
}

export interface DML_MWEB_COMMON_VideoInfo {
  /** vid */
  vid?: string;
  /** 帧率 */
  fps?: Int;
  /** 宽 */
  width?: Int;
  /** 高 */
  height?: Int;
  /** 时长 */
  duration?: Int;
  /** 视频的url 页面预览 */
  video_url?: string;
  /** aigc_flow: 视频的一些link信息 */
  link?: string;
  /** 不同场景的URL */
  scene_video_urls?: DML_MWEB_COMMON_SceneVideoUrl;
  /** 视频封面 */
  cover_url?: string;
  /** 视频格式，mp4、mov */
  format?: string;
  definition?: string;
  /** 水印类型 */
  logo_type?: string;
  encryption_key?: string;
  /** 视频md5 */
  md5?: string;
  /** 视频大小，单位byte */
  size?: Int;
  /** @deprecated 该字段和vid有重复，废弃，使用vid字段 */
  video_id?: string;
  /** 记录vid来自哪个item_id */
  aigc_video?: DML_MWEB_COMMON_MAigcVideo;
}

export interface DML_MWEB_COMMON_VideoTemplateConf {
  enable?: boolean;
  /** 视频模板对应的视频ItemID */
  video_template_item_id?: Int64;
  image_info?: DML_MWEB_COMMON_ImageInfo;
  /** 支持 VID */
  video_info?: DML_MWEB_COMMON_VideoInfo;
}

export interface DML_MWEB_COMMON_VoiceInfo {
  /** 音色 id 信息 */
  id_info: DML_MWEB_COMMON_IdInfo;
}

export interface DML_MWEB_COMMON_Word {
  /** 开始时间 ms */
  start_time?: Int;
  /** 结束时间 ms */
  end_time?: Int;
  /** 字 */
  text?: string;
}
/* eslint-enable */
