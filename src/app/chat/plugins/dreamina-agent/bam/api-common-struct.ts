// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as mweb_common from "./mweb_common";

export type Int64 = string | number;

export enum DML_API_COMMON_STRUCT_AccountType {
  normal = 0,
  test = 1,
}

export enum DML_API_COMMON_STRUCT_AigcDraftResourcePlatform {
  Unknown = 0,
  ImageX = 1,
  VCloud = 2,
}

export enum DML_API_COMMON_STRUCT_BoxEdgeType {
  Hard = 1,
  Soft = 2,
}

export enum DML_API_COMMON_STRUCT_CharacterStatus {
  Normal = 1,
  Deleted = 2,
}

export enum DML_API_COMMON_STRUCT_DirectionType {
  /** 向上翻页（时间倒序） */
  PageUp = 1,
  /** 向下翻页（时间正序） */
  PageDown = 2,
}

/** FeedBack 生成素材反馈（点赞/点踩）。
 字段名及枚举值与移动端对齐，见https://bytedance.larkoffice.com/docx/Ir4jd2Z5soM53Wx3vHGcHwxGnme */
export enum DML_API_COMMON_STRUCT_FeedBack {
  None = 0,
  Like = 1,
  Unlike = 2,
}

export enum DML_API_COMMON_STRUCT_GenerateTaskStatus {
  /** 未知，例如查询失败、或写入时未指定状态 */
  Unknown = -1,
  /** 刚创建的任务，前端可以重试 */
  Init = 0,
  /** 前置 tns 检查不通过 */
  PreTnsCheckNotPass = 10,
  /** 送生成成功，不可以重试，等待结果中 */
  SubmitOk = 20,
  /** 最终生成失败，不可重试 */
  FinalGenerateFail = 30,
  /** 后审不通过，不可重试 */
  PostTnsCheckNotPass = 40,
  /** 最终成功，此时 reload 点查一次对应的 history，里面会有对应生成的结果 */
  FinalSuccess = 50,
  /** 软删除 */
  Deleted = 100,
}

export enum DML_API_COMMON_STRUCT_GenResultCode {
  Success = 0,
  SystemBusy = 1,
  /** 智创网关失败 */
  PreTextRiskNotPass = 2,
  PreTextCopyrightNotPass = 3,
  PreImgRiskNotPass = 4,
  PostImgRiskNotPass = 5,
  AlgoRateLimitNotPass = 6,
  PreImgCopyrightNotPass = 7,
  PostImgCopyrightNotPass = 8,
  /** 超创用户审核失败 */
  SuperUserMachineReviewNotPass = 9,
  SuperUserManualReviewNotPass = 10,
}

export enum DML_API_COMMON_STRUCT_HistoryType {
  /** 所有图片，即包含2和3 */
  All = 1,
  /** 文生图 */
  Generate = 2,
  /** 超分图 */
  SuperResolution = 3,
  /** 文生视频 */
  Text2Video = 4,
  /** 人物生视频 */
  Avatar2Video = 5,
  /** 视频配乐 */
  VideoBGM = 6,
  /** 音声生成 */
  Audio = 7,
  /** 视频生成音效的产物 */
  VideoAudioEffect = 8,
}

export enum DML_API_COMMON_STRUCT_Image2AvatarProcessStatus {
  /** 创建失败 */
  Failed = -1,
  /** 创建成功 */
  Success = 0,
  /** 创建成功，但是效果不好，效果不好的原因由 refuse_code 和 message 字段标识 */
  PoorEffect = 3,
}

export enum DML_API_COMMON_STRUCT_ModelVisable {
  /** 可见 不可用 显示Beta */
  Visable = 1,
  /** 可用 显示New */
  Enable = 2,
}

export enum DML_API_COMMON_STRUCT_ShareStatus {
  Default = 0,
  /** 未生成 */
  NotGenerated = 1,
  /** 作品不允许分享 */
  NotAllowed = 2,
  /** 作品允许分享 */
  Allowed = 3,
}

export enum DML_API_COMMON_STRUCT_TaskPriority {
  /** 普通任务 */
  Normal = 0,
  /** 闲时任务 */
  Relax = 1,
}

/** 透传VDA的视频状态 */
export enum DML_API_COMMON_STRUCT_VDAVideoStatus {
  Uploading = 1,
  UploadFailed = 2,
  WaitingForUploading = 3,
  UploadSuccess = 4,
  EncodeSuccess = 10,
  EncodeFailed = 20,
  Encoding = 30,
  NonExist = 40,
}

/** 仅在视频首次从0到1生成的时候传 */
export enum DML_API_COMMON_STRUCT_VideoMode {
  /** 此为旧版本 v1.0 的视频生成模式,本身无意义, 1.0模型也没有生成模式的概念 */
  V1Default = 0,
  Preview = 1,
  /** 对应 ai lab v1.2 的 default 生成 */
  Default = 2,
  /** 智创 live_photo */
  ICLivePhoto = 3,
  /** 人物生成模型 */
  Avatar = 4,
  /** 人物生成Loopy模型，对应智创 Loopy 处理器 */
  Avatar_Loopy = 5,
  /** 人物生成高品质模型 */
  Avatar_Hq = 6,
}

export enum DML_API_COMMON_STRUCT_VideoModelStatus {
  /** 上线 */
  Online = 0,
  /** 下线，这个状态的模型服务端会过滤，端上不用管 */
  Offline = 1,
  /** 外部模型欠费，置灰不可用，但还是下发 */
  NoCredits = 2,
}

export enum DML_API_COMMON_STRUCT_VideoSizeType {
  /** 横屏 */
  Landscape = 1,
  /** 竖屏 */
  Portrait = 2,
  /** 方形（长宽差<=100） */
  Square = 3,
}

export enum DML_API_COMMON_STRUCT_VideoWatermarkType {
  /** 未知 */
  Unknow = 0,
  /** 无明水印（视频必带暗水印） */
  NoWatermark = 1,
  /** 暗水印+商业化明水印 */
  BusiWatermark = 2,
  /** 暗水印+AIGC明水印 */
  AigcWatermark = 3,
  /** 暗水印+商业化明水印+AIGC明水印 */
  BusiAigcWatermark = 4,
  /** 抖音分享场景，AIGC明水印 */
  DouyinShareWatermark = 5,
  /** 用户水印 */
  UserWatermark = 6,
  /** 用户水印+商业化水印 */
  BusiUserWatermark = 7,
  /** 用户水印+AIGC明水印 */
  AigcUserWatermark = 8,
  /** 用户水印+商业化水印+AIGC明水印 */
  BusiAigcUserWatermark = 9,
}

/** VProcessType 视频处理类型 */
export enum DML_API_COMMON_STRUCT_VProcessType {
  /** 视频生成, lab default */
  VideoGen = 1,
  /** 超分 */
  SR = 2,
  /** 补帧 */
  InsertFrame = 3,
  /** 对口型 */
  LipSync = 4,
  /** 延长 */
  Extend = 5,
  /** lab sr */
  LabSR = 6,
  /** 音视频合成 */
  MixAudioVideo = 7,
  /** 视频配乐 */
  VideoBGM = 8,
  /** 单图生口型视频 */
  LipSyncImage = 9,
  /** 用户视频对口型 */
  LipSyncUserVideo = 10,
  /** 视频模板 */
  VideoTemplate = 11,
  /** 视频配音效 */
  VideoAudioEffect = 12,
}

export interface DML_API_COMMON_STRUCT_AIEffect {
  /** 特效的ID */
  ai_effect_id?: string;
  /** 特效的状态，对应feed的itemStatus 102:上线 Offline:下架 */
  status?: Int64;
  /** 特效名称（用于列表做渲染） */
  name?: string;
  /** 列表示例输出图片/视频 */
  list_examples?: Array<mweb_common.DML_MWEB_COMMON_ImageInfo>;
  /** 详情页头图 */
  detail_header_images?: Array<mweb_common.DML_MWEB_COMMON_ImageInfo>;
  /** 多个输入项 */
  inputs?: Array<mweb_common.DML_MWEB_COMMON_AIEffectUserInput>;
  /** 商业化数据 */
  commerce_info?: mweb_common.DML_MWEB_COMMON_CommerceReqInfo;
  /** 作品宽 */
  work_width?: Int;
  /** 作品高 */
  work_height?: Int;
  /** 模型key 透传 */
  req_key?: string;
  /** generate_type 透传 */
  generate_type?: mweb_common.DML_MWEB_COMMON_AIGCGenerateType;
  /** 列表示例输入图片/视频 */
  list_examples_input?: Array<mweb_common.DML_MWEB_COMMON_ImageInfo>;
}

export interface DML_API_COMMON_STRUCT_AIGCByteEditPaintingParams {
  uri?: string;
  origin_item_id?: string;
  origin_prompt?: string;
  strength?: Double;
}

export interface DML_API_COMMON_STRUCT_AigcData {
  generate_type?: mweb_common.DML_MWEB_COMMON_AIGCGenerateType;
  /** 1. 生成视频场景, 需要用这个 id 去查询异步任务的生成状态 */
  history_record_id?: string;
  /** 1. 生成视频场景，略此字段 */
  origin_history_record_id?: string;
  /** 生成时间戳 */
  created_time?: Double;
  /** 1. 如果是生成视频的场景，并且视频已经生成 ok，则视频信息在这里 */
  item_list?: Array<DML_API_COMMON_STRUCT_EffectItem>;
  /** 1. 如果是生成视频的请求, 则这个里面有生成的预览图 */
  origin_item_list?: Array<DML_API_COMMON_STRUCT_OriginItem>;
  /** 对于某些长时间生成内容的体裁，会是异步的形式。此时可以用这个 id 去轮询结果, 对应 MGetGenerateTask */
  task?: DML_API_COMMON_STRUCT_GenerateTask;
  /** History所属的AIGCMod，例如工作台、画布、故事模式等 */
  mode?: string;
  /** 资产信息，例如资产是否被收藏等。 */
  asset_option?: DML_API_COMMON_STRUCT_AssetOption;
  /** History所属的用户uid */
  uid?: string;
  /** flow信息，里面包含了版本 */
  aigc_flow?: DML_API_COMMON_STRUCT_AIGCFlow;
  /** 历史记录的状态 */
  status?: DML_API_COMMON_STRUCT_GenerateTaskStatus;
  /** 历史记录的聚合key的md5，只有工作台生产的history才有这个 */
  history_group_key_md5?: string;
  /** 历史记录的聚合key，只有工作台生产的history才有这个 */
  history_group_key?: string;
  /** 前端提交的草稿内容 */
  draft_content?: string;
  /** 资源预解析 */
  resources?: Array<DML_API_COMMON_STRUCT_AigcDraftResource>;
  /** 生成失败的 item */
  failed_item_list?: Array<DML_API_COMMON_STRUCT_EffectItem>;
  /** 首次生成类型，目前只有人物创建形象场景会设置 */
  first_generate_type?: mweb_common.DML_MWEB_COMMON_AIGCGenerateType;
  /** 任务中间产物，比如生视频之前会先生成封面保存更新在这里 */
  task_rets?: Array<mweb_common.DML_MWEB_COMMON_TaskRet>;
  /** 具体失败的错误码 */
  fail_code?: string;
  submit_id?: string;
  capflow_id?: string;
  /** 端上透传的埋点字段，服务端不做处理 */
  metrics_extra?: string;
  ref_item?: DML_API_COMMON_STRUCT_EffectRefItem;
  fail_msg?: string;
  /** logid */
  generate_id?: string;
  /** 任务结束时间 */
  finish_time?: Int64;
  /** 模型信息 */
  model_info?: DML_API_COMMON_STRUCT_ModelInfo;
  /** 预估生成耗时 - 单位:s */
  forecast_generate_cost?: Int64;
  /** 预估排队耗时 - 单位:s */
  forecast_queue_cost?: Int64;
  /** 视频模板 */
  video_template_item?: DML_API_COMMON_STRUCT_EffectRefItem;
  /** EffectType = ItemType_AIEffectTemplate // 即梦AI特效模板 和 即梦AI特效作品 才有下面这个字段 */
  ai_effect?: DML_API_COMMON_STRUCT_AIEffect;
  fail_starling_key?: string;
  fail_starling_message?: string;
  /** 基于草稿协议生成，用到的min_feats */
  min_feats?: Array<string>;
}

export interface DML_API_COMMON_STRUCT_AigcDraftResource {
  /** 内容标识，用于端上定位资源数据 */
  key?: string;
  /** 内容类型，用于不同的素材类型，目前只支持 image|video */
  type?: string;
  name?: string;
  /** 资源来源 */
  platform?: DML_API_COMMON_STRUCT_AigcDraftResourcePlatform;
  /** 图片信息 */
  image_info?: DML_API_COMMON_STRUCT_ImageInfo;
  /** 视频信息 */
  video_info?: DML_API_COMMON_STRUCT_EffectVideo;
  /** 公共资产库信息 */
  asset_info?: DML_API_COMMON_STRUCT_CommonAssets;
}

export interface DML_API_COMMON_STRUCT_AIGCFlow {
  /** 用来做版本控制的字段，通过req query传入的aigc_flow_version做判断，req.query.aigc_flow_version ≥ version时表示客户端支持使用(dreamina app mvp版本是0.1.0)
如果客户端没传aigc_flow_version，默认可用 */
  version?: string;
}

export interface DML_API_COMMON_STRUCT_AigcImage {
  /** 生图再生视频的场景，传入用到的生图结果id，类型是字符串 */
  item_id?: string;
  /** 图片的AIGC参数，在返回视频item时填充图片item数据 */
  aigc_image_params?: DML_API_COMMON_STRUCT_AIGCImageMetaData;
  /** 图片的通用属性，在返回视频item时填充图片item数据 */
  common_attr?: DML_API_COMMON_STRUCT_EffectCommonAttr;
  aigc_draft?: mweb_common.DML_MWEB_COMMON_AigcDraft;
}

export interface DML_API_COMMON_STRUCT_AIGCImageMetaData {
  /** 生成方式 */
  generate_type?: mweb_common.DML_MWEB_COMMON_AIGCGenerateType;
  /** 上次生成方式 */
  last_generate?: mweb_common.DML_MWEB_COMMON_AIGCGenerateType;
  /** 第一次生成方式 */
  first_generate_type?: mweb_common.DML_MWEB_COMMON_AIGCGenerateType;
  /** 文生视频参数 */
  text2video_params?: DML_API_COMMON_STRUCT_Text2VideoMetaData;
  text2image_params?: DML_API_COMMON_STRUCT_AIGCText2ImageParams;
  /** 超分参数 */
  super_resolution_params?: DML_API_COMMON_STRUCT_AIGCSuperResolutionParams;
  /** inpaint_edit 参数 */
  in_painting_params?: DML_API_COMMON_STRUCT_AIGCInPaintingParams;
  /** inpaint_remove 参数 */
  in_painting_remove_params?: DML_API_COMMON_STRUCT_AIGCInPaintingRemoveParams;
  /** 扩图参数 */
  out_painting_params?: DML_API_COMMON_STRUCT_AIGCOutPaintingParams;
  /** 垫图参数 */
  blend_params?: DML_API_COMMON_STRUCT_BlendParams;
  /** prompt来源，埋点使用 */
  prompt_source?: string;
  /** 模板来源 */
  template_source?: string;
  /** 连续生成次数 */
  generate_count?: Int64;
  /** 做同款的模板item id */
  template_id?: string;
  /** 请求id，每张图片不一样id */
  request_id?: string;
  /** 请求生成id，文生图4张图片一样的generate_id */
  generate_id?: string;
  /** 文生图、图生图、垫图 会产生新id */
  origin_request_id?: string;
  /** 上一次生成的request_id 只有后编辑才有 */
  last_request_id?: string;
  /** 整条生图链路各aigc能力使用次数，下标分别对应0：细节重绘 1：inpaint_edit 2：outpaint 3：inpaint_remove 4：hd 5：i2i 6:face-gan
7: bgpaint 8:controlnet */
  aigc_cnt_list?: Array<Int>;
  /** 超清参数 */
  normal_hd_params?: DML_API_COMMON_STRUCT_AIGCNormalHdParams;
  /** AigcMode */
  aigc_mode?: string;
  /** 融合参数 */
  fusion_params?: mweb_common.DML_MWEB_COMMON_FusionParams;
  /** insta drag参数 */
  insta_drag_params?: mweb_common.DML_MWEB_COMMON_InstaDragParams;
  /** 做同款合集下的子图id */
  sub_template_id?: string;
  /** 配乐参数，供二次编辑使用 */
  video_bgm_params?: mweb_common.DML_MWEB_COMMON_VideoBGMParams;
  /** 隐藏参考图 */
  hide_ref_images?: boolean;
  /** 对口型 */
  lip_sync?: mweb_common.DML_MWEB_COMMON_LipSyncOpt;
  lip_sync_image?: DML_API_COMMON_STRUCT_MLipSyncImageOpt;
  /** 指令编辑参数 */
  byte_edit_params?: DML_API_COMMON_STRUCT_AIGCByteEditPaintingParams;
  /** 发布配置 */
  publish_opt?: mweb_common.DML_MWEB_COMMON_PublishOpt;
  /** 视频配音效参数 */
  video_audio_effect_params?: mweb_common.DML_MWEB_COMMON_VideoAudioEffectInput;
  /** 发布时携带的公共资产库 */
  publish_asset_list?: Array<DML_API_COMMON_STRUCT_CommonAssets>;
}

export interface DML_API_COMMON_STRUCT_AIGCInPaintingParams {
  mask_uri?: string;
  origin_item_id?: string;
  origin_prompt?: string;
  mask_url?: string;
}

export interface DML_API_COMMON_STRUCT_AIGCInPaintingRemoveParams {
  mask_uri?: string;
  origin_item_id?: string;
  origin_prompt?: string;
  mask_url?: string;
}

export interface DML_API_COMMON_STRUCT_AIGCNormalHdParams {
  last_generate_type?: mweb_common.DML_MWEB_COMMON_AIGCGenerateType;
  origin_item_id?: string;
}

export interface DML_API_COMMON_STRUCT_AIGCOutPaintingParams {
  origin_item_id?: string;
  up_scale?: DML_API_COMMON_STRUCT_algoProxyUtilsOutPaintingUpScale;
  origin_prompt?: string;
}

export interface DML_API_COMMON_STRUCT_AIGCSuperResolutionParams {
  last_generate_type?: mweb_common.DML_MWEB_COMMON_AIGCGenerateType;
  origin_item_id?: string;
  /** 是否使用图生文prompt能力 */
  is_describe_prompt?: boolean;
}

export interface DML_API_COMMON_STRUCT_AIGCText2ImageParams {
  /** 图片比例 */
  image_ratio?: mweb_common.DML_MWEB_COMMON_ImageRatioType;
  /** 用户使用的模型 */
  model_config?: DML_API_COMMON_STRUCT_ModelConfig;
  /** 用户输入的model */
  prompt?: string;
  /** 生成图像的采样步数,默认值：20，取值范围[1-50] */
  sample_steps?: Int64;
  /** [1, 21亿]
随机种子，-1为不随机种子；其他为指定随机种子,默认值：-1 */
  seed?: Int64;
  /** 用户输入的屏蔽词 */
  user_negative_prompt?: string;
  /** 结果图尺寸信息 */
  large_image_info?: mweb_common.DML_MWEB_COMMON_ReqImageInfo;
  /** 原始模型信息 */
  origin_model_config?: DML_API_COMMON_STRUCT_ModelConfig;
  /** 实际模型信息 */
  actual_model_config?: DML_API_COMMON_STRUCT_ModelConfig;
  /** 用户传参的采样强度 */
  sample_strength?: Double;
  /** 是否使用图生文prompt能力 */
  is_describe_prompt?: boolean;
  /** 是否使用pe */
  use_pe?: boolean;
  /** 模型调度配置 */
  schedule_conf?: string;
  /** 实际请求算法prompt */
  actual_prompt?: string;
  /** 实际请求算法reqKey */
  actual_request_key?: string;
}

export interface DML_API_COMMON_STRUCT_algoProxyUtilsOutPaintingUpScale {
  /** 图片下边扩增区域对原图高的占比，无扩增表示为0, 取值[0,x) */
  bottom?: Double;
  /** 图片左边扩增区域对原图宽的占比，无扩增表示为0, 取值[0,x) */
  left?: Double;
  /** 图片右边扩增区域对原图宽的占比，无扩增表示为0, 取值[0,x) */
  right?: Double;
  /** 图片上边扩增区域对原图高的占比，无扩增表示为0, 取值[0,x) */
  top?: Double;
  /** 图片最长边 */
  max_size?: Int64;
  /** 图片比例 */
  image_ratio?: mweb_common.DML_MWEB_COMMON_ImageRatioType;
}

export interface DML_API_COMMON_STRUCT_AssetOption {
  /** 资产是否被收藏 */
  has_favorited?: boolean;
  /** 收藏资产的素材id列表 */
  favorite_item_id_list?: Array<string>;
}

export interface DML_API_COMMON_STRUCT_AudioVideoMixConf {
  enable?: boolean;
  /** 用于音视频合成的视频ItemID */
  video_item_id?: string;
  /** 用于音视频合成的音频VID */
  audio_vid?: string;
  /** 音视频合成任务的参数 */
  param_json?: string;
  /** 音视频合成场景，0：视频配乐结果的合成，1：视频配音效结果的合成 */
  scene?: mweb_common.DML_MWEB_COMMON_GenerateVideoAudioScene;
}

export interface DML_API_COMMON_STRUCT_BlendAbility {
  name?: string;
  large_image_list?: Array<DML_API_COMMON_STRUCT_ImageInfo>;
  cover_image_list?: Array<DML_API_COMMON_STRUCT_ImageInfo>;
  control_net_list?: Array<DML_API_COMMON_STRUCT_ControlNetParams>;
  extra?: string;
  strength?: Double;
  /** 角色引用生图参数 */
  ip_keep_list?: Array<DML_API_COMMON_STRUCT_IpKeepParam>;
  /** 风格参考参数 */
  style_reference?: DML_API_COMMON_STRUCT_StyleReference;
  /** 风格代码 */
  style_code?: string;
  common_asset?: DML_API_COMMON_STRUCT_CommonAssets;
}

/** 整体废弃不再使用
 标记模型可以支持哪些垫图能力 */
export interface DML_API_COMMON_STRUCT_BlendEnable {
  /** 人脸保持 */
  face_swap?: boolean;
  /** 主体保持 */
  bg_paint?: boolean;
  /** 轮廓 */
  canny?: boolean;
  /** 景深 */
  depth?: boolean;
  /** 姿势 */
  pose?: boolean;
}

export interface DML_API_COMMON_STRUCT_BlendParams {
  ability_list?: Array<DML_API_COMMON_STRUCT_BlendAbility>;
  has_item_urls?: boolean;
  model?: string;
  sample_steps?: Int64;
  extra?: string;
  /** prompt引用的图片具体属于哪个能力以及图片列表具体下标 */
  prompt_placeholder_info_list?: Array<mweb_common.DML_MWEB_COMMON_BlendPromptPlaceHolderInfo>;
  /** 风格迁移参数 */
  style_reference?: DML_API_COMMON_STRUCT_StyleReference;
  /** 垫图的数据包url，与EffectCommonAttr.item_urls同源 */
  item_urls?: Array<string>;
  /** 用户传参的采样强度 */
  sample_strength?: Double;
}

export interface DML_API_COMMON_STRUCT_BoxEdges {
  /** 长度为2, 为矩形的左上,右下顶点坐标, 其中竖轴为 y 轴
矩形平行于坐标轴时,矩形的两个对角顶点(vertex) 坐标唯一确定一个矩形的四条边 (edge) */
  vertexs?: Array<Array<Double>>;
  edge_type?: DML_API_COMMON_STRUCT_BoxEdgeType;
}

export interface DML_API_COMMON_STRUCT_Boximator {
  enable?: boolean;
  /** 可以放置多个 box, 每个 box 有起始位置 */
  boxes?: Array<DML_API_COMMON_STRUCT_BoximatorBox>;
  boximator_image?: DML_API_COMMON_STRUCT_ImageInfo;
  /** boximator 还原需要较多信息,fe 可以在这个字段中存储相关还原需要的信息 */
  draft?: string;
  /** draft对应的文件，后续boximator都上传文件uri */
  draft_file?: mweb_common.DML_MWEB_COMMON_FileInfo;
}

export interface DML_API_COMMON_STRUCT_BoximatorBox {
  /** 表示 box 的颜色 16进制的 rgb, #ff0000 */
  color?: string;
  /** 一个 BoximatorBox 表示一个矩形的运动轨迹, 可以输入轨迹上的多个 box 以便算法做更精细的生成,
start_box, end_box 必传, 中间的 box 暂时不支持, 当前的语义 boxes[0] 是 start_box, boxes[len(boxes)-1] 是 end_box */
  boxes?: Array<DML_API_COMMON_STRUCT_BoxEdges>;
  /** motion_path box 运动轨迹, 直线部分可以减少采样,曲线部分可以加多采样 */
  motion_path?: Array<Array<Double>>;
  /** prompt 表示主体的简略描述, 比如宇航员,狮子 */
  prompt?: string;
}

export interface DML_API_COMMON_STRUCT_CloneVoiceInfo {
  /** 音色生成状态; 如果音色还在生成中，通过 item_id 走 /mweb/v1/get_local_item_list 查询 */
  status?: mweb_common.DML_MWEB_COMMON_CloneVoiceStatus;
  /** 具体失败的错误码 */
  fail_code?: string;
  /** 创建音色时的 submit_id */
  submit_id?: string;
}

export interface DML_API_COMMON_STRUCT_CommerceReqInfo {
  resource_sub_type?: string;
  resource_id_type?: string;
  resource_id?: string;
  /** 权益类型 */
  benefit_type?: string;
  /** 消耗权益计量，如视频s数 */
  amount?: Int;
}

export interface DML_API_COMMON_STRUCT_CommonAssets {
  /** 资产类型 */
  asset_type?: string;
  /** 资产代码 */
  asset_code?: string;
  /** 资产状态 */
  status?: mweb_common.DML_MWEB_COMMON_CommonAssetsStatus;
  /** 资产引用的原始模板图片信息 */
  refer_image_list?: Array<mweb_common.DML_MWEB_COMMON_CommonAssetsReferImage>;
  /** 基于此资产码生成的item_ids */
  generate_item_ids?: Array<string>;
}

export interface DML_API_COMMON_STRUCT_CompetitionAward {
  title: string;
  subtitle: string;
  prize: string;
  subprize: Array<string>;
  count: Int64;
}

export interface DML_API_COMMON_STRUCT_CompetitionData {
  competition_key: string;
  competition_title: string;
  award?: DML_API_COMMON_STRUCT_CompetitionAward;
}

export interface DML_API_COMMON_STRUCT_ContentAttr {
  /** 运营精选标签列表 */
  operation_select_tags?: Array<DML_API_COMMON_STRUCT_Tag>;
  /** 第一内容题材标签 */
  first_topic_tag?: DML_API_COMMON_STRUCT_Tag;
  /** 第二内容题材标签 */
  second_topic_tag?: DML_API_COMMON_STRUCT_Tag;
}

export interface DML_API_COMMON_STRUCT_ControlNetParams {
  name?: string;
  strength?: Double;
  image_index?: Int;
  /** bgpaint能力的mask图下标 */
  mask_index?: Int;
}

export interface DML_API_COMMON_STRUCT_EffectActData {
  weekly_challenge_list?: Array<DML_API_COMMON_STRUCT_EffectWeeklyChallengeData>;
}

export interface DML_API_COMMON_STRUCT_EffectAudio {
  vid?: string;
  origin_audio?: mweb_common.DML_MWEB_COMMON_AudioInfo;
}

export interface DML_API_COMMON_STRUCT_EffectCanvas {
  has_project_file?: boolean;
}

export interface DML_API_COMMON_STRUCT_EffectCollection {
  item_list?: Array<DML_API_COMMON_STRUCT_EffectItem>;
}

export interface DML_API_COMMON_STRUCT_EffectCommentInfo {
  /** 作品关联的评论id */
  link_comment_id?: string;
}

export interface DML_API_COMMON_STRUCT_EffectCommonAttr {
  id?: string;
  effect_id?: string;
  /** 5: 视频短片 9:单图 109:组图  53:视频模板 */
  effect_type?: Int64;
  title?: string;
  description?: string;
  cover_url?: string;
  item_urls?: Array<string>;
  md5?: string;
  third_resource_id?: string;
  create_time?: Int64;
  status?: Int64;
  review_info?: DML_API_COMMON_STRUCT_ReviewInfo;
  aspect_ratio?: Double;
  publish_source?: string;
  collection_ids?: Array<string>;
  extra?: string;
  has_published?: boolean;
  published_item_id?: string;
  /** 多种尺寸的封面，key为长边像素数，例如"1080"，value为图片url */
  cover_url_map?: Record<string, string>;
  local_item_id?: string;
  web_extra?: string;
  loki_info?: string;
  update_time?: Int64;
  cover_uri?: string;
  starling_key?: string;
  loki_effect_id?: string;
  /** 智能裁剪裁剪坐标，key为uniqkey，value为裁剪坐标X-Smart-Crop-Loc，例如"(0,100)-(400,400)" */
  smart_crop_loc?: Record<string, string>;
  /** 封面主色调, 格式(R,G,B): #FFFFFF */
  cover_main_color?: string;
  /** 封面图片主题检测, */
  cover_crop_loc?: Record<string, Array<Array<Double>>>;
  /** 封面图高度 */
  cover_height?: Int;
  /** 封面图宽度 */
  cover_width?: Int;
}

export interface DML_API_COMMON_STRUCT_EffectGenResultData {
  result_code?: DML_API_COMMON_STRUCT_GenResultCode;
  result_msg?: string;
}

export interface DML_API_COMMON_STRUCT_EffectImage {
  format?: string;
  large_images?: Array<DML_API_COMMON_STRUCT_ImageInfo>;
}

export interface DML_API_COMMON_STRUCT_EffectItem {
  common_attr?: DML_API_COMMON_STRUCT_EffectCommonAttr;
  author?: DML_API_COMMON_STRUCT_User;
  image?: DML_API_COMMON_STRUCT_EffectImage;
  video?: DML_API_COMMON_STRUCT_EffectVideo;
  collection?: DML_API_COMMON_STRUCT_EffectCollection;
  aigc_image_params?: DML_API_COMMON_STRUCT_AIGCImageMetaData;
  statistic?: DML_API_COMMON_STRUCT_Statistic;
  /** 素材所属分类ID列表 */
  category_id_list?: Array<Int>;
  competition_data?: DML_API_COMMON_STRUCT_CompetitionData;
  /** 活动数据 */
  act_data?: DML_API_COMMON_STRUCT_EffectActData;
  /** 短片数据 */
  short_video_data?: DML_API_COMMON_STRUCT_ShortVideoData;
  /** flow信息，里面包含了版本 */
  aigc_flow?: DML_API_COMMON_STRUCT_AIGCFlow;
  /** 做同款信息 */
  ref_item?: DML_API_COMMON_STRUCT_EffectRefItem;
  /** 客户端追踪数据 */
  client_trace_data?: mweb_common.DML_MWEB_COMMON_ClientTraceData;
  comment_info?: DML_API_COMMON_STRUCT_EffectCommentInfo;
  /** 配乐数据，仅视GenerateType为VideoBGM的素材有值 */
  bgm_data?: DML_API_COMMON_STRUCT_VideoBGMData;
  /** 视频配乐音视频合成数据，仅GenerateType为VideoBGM的素材有值 */
  mix_av_data?: DML_API_COMMON_STRUCT_MixAudioVideoData;
  /** 草稿信息 */
  aigc_draft?: mweb_common.DML_MWEB_COMMON_AigcDraft;
  /** 草稿中预解析内容 */
  aigc_draft_resources?: Array<DML_API_COMMON_STRUCT_AigcDraftResource>;
  /** aigcflow产物信息 */
  aigc_ret?: mweb_common.DML_MWEB_COMMON_AigcRet;
  /** 生图结果信息 */
  gen_result_data?: DML_API_COMMON_STRUCT_EffectGenResultData;
  /** 音乐生成结果 */
  audio?: DML_API_COMMON_STRUCT_EffectAudio;
  /** 画布模板信息 */
  canvas?: DML_API_COMMON_STRUCT_EffectCanvas;
  /** Extra信息 */
  extra?: DML_API_COMMON_STRUCT_EffectItemExtra;
  /** 内容标签 */
  content_attr?: DML_API_COMMON_STRUCT_ContentAttr;
  /** AI Feature */
  ai_feature?: mweb_common.DML_MWEB_COMMON_AIFeatureData;
  /** 视频模板 */
  video_template_item?: DML_API_COMMON_STRUCT_EffectRefItem;
  /** EffectType = ItemType_AIEffectTemplate // 即梦AI特效模板 和 即梦AI特效作品 才有下面这个字段 */
  ai_effect?: DML_API_COMMON_STRUCT_AIEffect;
  sharing_info?: DML_API_COMMON_STRUCT_EffectSharingInfo;
  /** 音色克隆信息 */
  clone_voice_info?: DML_API_COMMON_STRUCT_CloneVoiceInfo;
  /** 配音效数据，仅GenerateType为VideoAudioEffect的素材有值 */
  video_audio_effect?: DML_API_COMMON_STRUCT_VideoAudioEffectData;
}

export interface DML_API_COMMON_STRUCT_EffectItemExtra {
  /** 作品debug信息 */
  debug_info?: DML_API_COMMON_STRUCT_ItemDebugInfo;
  /** 来源于服务端的ab信息，用于埋点 */
  ab_tags?: string;
  /** 视频模板: 是否预置模板 */
  video_template_official?: boolean;
}

export interface DML_API_COMMON_STRUCT_EffectRefItem {
  common_attr?: DML_API_COMMON_STRUCT_EffectRefItemCommonAttr;
  author?: DML_API_COMMON_STRUCT_User;
  video?: DML_API_COMMON_STRUCT_EffectVideo;
  /** Extra信息 */
  extra?: DML_API_COMMON_STRUCT_EffectItemExtra;
}

export interface DML_API_COMMON_STRUCT_EffectRefItemCommonAttr {
  id?: string;
  effect_id?: string;
  /** 5: 视频短片 9:单图 109:组图  53:视频模板 */
  effect_type?: Int64;
  description?: string;
  cover_url?: string;
  /** 多种尺寸的封面，key为长边像素数，例如"1080"，value为图片url */
  cover_url_map?: Record<string, string>;
  /** 智能裁剪裁剪坐标，key为uniqkey，value为裁剪坐标X-Smart-Crop-Loc，例如"(0,100)-(400,400)" */
  smart_crop_loc?: Record<string, string>;
  status?: Int64;
  title?: string;
}

/** 分享信息， */
export interface DML_API_COMMON_STRUCT_EffectSharingInfo {
  share_status?: DML_API_COMMON_STRUCT_ShareStatus;
  share_image?: DML_API_COMMON_STRUCT_EffectImage;
  share_video?: DML_API_COMMON_STRUCT_EffectVideo;
  hash_tags?: Array<DML_API_COMMON_STRUCT_HashTag>;
}

export interface DML_API_COMMON_STRUCT_EffectVideo {
  /** 视频vid */
  video_id?: string;
  /** 视频时长，单位s */
  duration?: Int64;
  /** 原视频信息 */
  origin_video?: mweb_common.DML_MWEB_COMMON_VideoInfo;
  /** 转码后的视频，key为视频清晰度 */
  transcoded_video?: Record<string, mweb_common.DML_MWEB_COMMON_VideoInfo>;
  /** 视频尺寸类型, 长视频、宽视频、正方形视频 */
  video_size_type?: DML_API_COMMON_STRUCT_VideoSizeType;
  /** 视频封面 */
  cover_uri?: string;
  /** 视频转码状态，0表示转码中，1表示转码成功，2表示转码失败，仅品牌素材资源需要关注 */
  transcode_status?: Int64;
  /** 视频时长，单位毫秒 */
  duration_ms?: Int64;
  /** 视频雪碧图 */
  thumb?: DML_API_COMMON_STRUCT_Thumb;
  /** 视频水印类型 */
  watermark_type?: DML_API_COMMON_STRUCT_VideoWatermarkType;
  /** 视频封面 URL */
  cover_url?: string;
  /** 时长信息 */
  duration_info?: string;
  /** 音量信息 */
  volume_info?: DML_API_COMMON_STRUCT_VolumeInfo;
  /** 直接透传vda的视频状态，例如短片根据该状态码展示是否正在转码中 */
  vda_status?: DML_API_COMMON_STRUCT_VDAVideoStatus;
  /** 视频状态信息 */
  video_model?: string;
  /** 视频是否有音量 */
  has_audio?: boolean;
}

export interface DML_API_COMMON_STRUCT_EffectWeeklyChallengeData {
  act_key?: string;
  act_name?: string;
  award_ranking?: Int64;
  /** 活动卡片类型, 活动端内跳转使用 */
  act_schema?: string;
}

export interface DML_API_COMMON_STRUCT_EntranceMaterial {
  /** 标题 */
  title?: string;
  /** 副标题 */
  sub_title?: string;
  /** 主按钮 */
  button?: string;
  /** 主按钮跳转链接 */
  button_schema?: string;
  /** 图片 */
  image_infos?: Array<DML_API_COMMON_STRUCT_ImageInfo>;
  /** 视频 */
  video_infos?: Array<mweb_common.DML_MWEB_COMMON_VideoInfo>;
}

export interface DML_API_COMMON_STRUCT_ExtendConf {
  enable?: boolean;
  /** 希望延长的 ms 数 */
  extend_duration_ms?: Int;
  /** 延长部分的 fps */
  extend_fps?: Int;
  /** 延长部分的 prompt */
  extend_prompt?: string;
}

/** 异步生成任务 */
export interface DML_API_COMMON_STRUCT_GenerateTask {
  /** 后端生成唯一标识一个任务的 id */
  task_id?: string;
  /** 前端传入用于请求幂等的 submit_id */
  submit_id?: string;
  aid?: Int64;
  status?: DML_API_COMMON_STRUCT_GenerateTaskStatus;
  finish_time?: Int64;
  history_id?: string;
  task_payload?: DML_API_COMMON_STRUCT_TaskPayload;
  /** 生视频时用到的首帧图 */
  first_frame_image?: DML_API_COMMON_STRUCT_ImageInfo;
  /** 原始的输入信息 */
  original_input?: DML_API_COMMON_STRUCT_Text2VideoParams;
  /** deprecated，请使用multi_size_first_frame_image */
  req_first_frame_image?: Array<DML_API_COMMON_STRUCT_ImageInfo>;
  /** 如果没有传入 promot, 则会有这个字段 */
  ai_gen_prompt?: string;
  priority?: DML_API_COMMON_STRUCT_TaskPriority;
  lip_sync_info?: DML_API_COMMON_STRUCT_LipSyncInfo;
  /** 生视频时用到的尾帧图 */
  end_frame_image?: DML_API_COMMON_STRUCT_ImageInfo;
  /** 多尺寸首帧图。仅当请求体中配置multi_size_image_config时该字段会有值。 */
  multi_size_first_frame_image?: Array<DML_API_COMMON_STRUCT_ImageInfo>;
  /** 多尺寸尾帧图。仅当请求体中配置multi_size_image_config时该字段会有值。 */
  multi_size_end_frame_image?: Array<DML_API_COMMON_STRUCT_ImageInfo>;
  /** 视频处理过程 */
  process_flows?: Array<DML_API_COMMON_STRUCT_TaskProcessRecord>;
  create_time?: Int64;
  aigc_image_params?: DML_API_COMMON_STRUCT_AIGCImageMetaData;
  /** 做同款用到的模板信息 */
  ref_item?: DML_API_COMMON_STRUCT_EffectRefItem;
  image_to_avatar?: DML_API_COMMON_STRUCT_ImageToAvatar;
  first_generate_type?: Int64;
  resp_ret?: DML_API_COMMON_STRUCT_GenerateTaskRespRet;
}

export interface DML_API_COMMON_STRUCT_GenerateTaskRespRet {
  ret?: string;
}

export interface DML_API_COMMON_STRUCT_GetHistoryData {
  /** 是否还有更多数据 */
  has_more?: boolean;
  /** 下一页的 offset */
  next_offset?: Int64;
  records_list?: Array<DML_API_COMMON_STRUCT_AigcData>;
}

export interface DML_API_COMMON_STRUCT_GetHistoryReq {
  /** Deprecated，建议使用offset入参替代 */
  record_before_created_time?: Double;
  /** 分页开始时间，毫秒级时间戳，首页请求不传 */
  offset?: Int64;
  /** 一页拉取的数量 */
  count?: Int64;
  /** 拉取的生成记录类型 */
  type?: DML_API_COMMON_STRUCT_HistoryType;
  /** 排序方向 */
  direction?: DML_API_COMMON_STRUCT_DirectionType;
  /** 指定打包封面图的参数，降低前端展示加载耗时 */
  image_info?: mweb_common.DML_MWEB_COMMON_ReqImageInfo;
  /** 指定打包封面图的参数，降低前端展示加载耗时 */
  origin_image_info?: mweb_common.DML_MWEB_COMMON_ReqImageInfo;
  /** 是否打包父记录 */
  is_pack_origin?: boolean;
  /** derprecated，请使用history_option.multi_size_image_config */
  first_frame_requires?: Array<mweb_common.DML_MWEB_COMMON_ReqImageInfo>;
  /** AIGC场景，可选值为workbench、canvas、story，默认为workbench */
  mode?: string;
  history_option?: DML_API_COMMON_STRUCT_HistoryOption;
  /** 过滤history type，作为type字段的扩展 */
  history_type_list?: Array<DML_API_COMMON_STRUCT_HistoryType>;
}

export interface DML_API_COMMON_STRUCT_GetHistoryResponse {
  ret?: string;
  errmsg?: string;
  systime?: string;
  logid?: string;
  data?: DML_API_COMMON_STRUCT_GetHistoryData;
}

/** 客户端需要tag中返回string */
export interface DML_API_COMMON_STRUCT_HashTag {
  id?: string;
  name?: string;
  tag_id?: string;
}

/** 历史记录保存、查询相关参数 */
export interface DML_API_COMMON_STRUCT_HistoryOption {
  /** 当AIGCMode为story时，需要传入story_id以便隔离不同story下的历史记录 */
  story_id?: string;
  /** 当AIGCMode为character时，需要传入character_id以便隔离不同character下的历史记录 */
  character_id?: string;
  /** 多尺寸图片配置，不配置则不返回。目前只影响AIGC视频的首尾帧图。 */
  multi_size_image_config?: Array<mweb_common.DML_MWEB_COMMON_ReqImageInfo>;
  /** 生成任务状态过滤。不填则不过滤。对图片、视频历史有效。 */
  with_task_status?: Array<DML_API_COMMON_STRUCT_GenerateTaskStatus>;
  /** 仅获取已收藏的 */
  only_favorited?: boolean;
  /** 过滤对应aigc generate tyoe的历史记录 */
  aigc_generate_type_filters?: Array<mweb_common.DML_MWEB_COMMON_AIGCGenerateType>;
}

export interface DML_API_COMMON_STRUCT_I2VOpt {
  realman_avatar?: DML_API_COMMON_STRUCT_RealmanAvatar;
  live_photo?: DML_API_COMMON_STRUCT_Livephoto;
}

/** ImageInfo 作为请求参数，2,3,4字段业务后端会忽略
 业务会优先处理image_uri字段，如果没有再取ever_photo字段
 业务后端返回，仅返回1,2字段，在有些场景下，会返回cover_url_map字段，比如垫图功能下 */
export interface DML_API_COMMON_STRUCT_ImageInfo {
  /** 作为请求参数，会优先取image_uri参数 */
  image_uri?: string;
  /** 无论前端请求使用image_uri还是用云相册资产ID，
业务都会转存和下发image_uri和image_url */
  image_url?: string;
  width?: Int;
  height?: Int;
  format?: string;
  /** 云相册信息，当请求进来时会对这个内部做打包操作，转换成image_uri，后续的处理过程无需考虑ever_photo的存在 */
  ever_photo?: mweb_common.DML_MWEB_COMMON_EverPhoto;
  /** 生图图片信息，当请求进来时会对这个内部做打包操作，转换成image_uri，后续的处理过程无需考虑local_image的存在 */
  aigc_image?: DML_API_COMMON_STRUCT_AigcImage;
  /** 多尺寸图片，不一定所有图片都有这个字段 */
  cover_url_map?: Record<string, string>;
  /** 原图大小的url，如果是AIGC的图片，会带上水印下发 */
  origin_url?: string;
  /** 图片标题信息 作为请求参数 */
  title?: string;
  /** 图片的一些link信息 */
  link?: string;
  /** 智能裁剪裁剪坐标，key为uniqkey，value为裁剪坐标X-Smart-Crop-Loc，例如"(0,100)-(400,400)" */
  smart_crop_loc?: Record<string, string>;
  /** 是否是动图 */
  is_animated?: boolean;
}

export interface DML_API_COMMON_STRUCT_ImageToAvatar {
  /** 标准模型角色 id */
  resource_id_std?: string;
  /** Loopy模型角色 id, 对应前端生动模型 */
  resource_id_loopy?: string;
  /** 生成结果 */
  status?: DML_API_COMMON_STRUCT_Image2AvatarProcessStatus;
  /** 效果不好的具体规则 code 列表，枚举定义见：https://bytedance.larkoffice.com/docx/LXIVdKPatoIdJGxIBOGcJKlqnEh */
  refuse_code?: Array<Int>;
  /** status 为 3 时，返回驱动效果不好的原因 */
  message?: string;
  /** 形象创建的人脸位置，长度为4，[left, top, right, bottom]；多张人脸的情况下，返回最大的人脸 */
  face_position?: Array<Int64>;
}

export interface DML_API_COMMON_STRUCT_InsertFrameConf {
  enable?: boolean;
  /** 目标帧率 */
  target_fps?: Int;
  /** 原帧率 */
  origin_fps?: Int;
  /** 视频时长,单位 ms */
  duration_ms?: Int;
}

export interface DML_API_COMMON_STRUCT_IpKeepParam {
  /** 角色描述 */
  description?: string;
  /** 引用角色的id */
  character_id?: string;
  /** 引用角色的名称 */
  character_name?: string;
  /** 参考图主体的权重，越大生成结果和参考图中主体的相似度越高,[0， 1] */
  ref_ip_weight?: Double;
  /** 参考图人脸的权重，越大生成结果和参考图中人脸的相似度越高,[0， 1] */
  ref_id_weight?: Double;
  /** 角色状态 */
  status?: DML_API_COMMON_STRUCT_CharacterStatus;
}

export interface DML_API_COMMON_STRUCT_ItemDebugInfo {
  /** 作品展现次数 */
  vv?: Int64;
  /** 作品创建时间 */
  create_time?: Int64;
  /** 作品过审时间 */
  review_time?: Int64;
  /** 对任意用户的最早展现时间 */
  first_show_time?: Int64;
  /** 对当前用户的最早展现时间 */
  user_first_show_time?: Int64;
  /** 作品运营标签 */
  operation_tags?: Array<DML_API_COMMON_STRUCT_Tag>;
  /** 作品质量分 */
  quality_score?: Int64;
  /** 作品状态,  102:上线, 103:个人主页可见, 140:隐私状态, 141:待审, 144:自见 */
  status?: Int64;
}

export interface DML_API_COMMON_STRUCT_LabSrConf {
  enable?: boolean;
  sr_fps?: Int;
  /** 视频生成时表示生成多长的视频 */
  sr_duration_ms?: Int;
}

export interface DML_API_COMMON_STRUCT_LipSyncAudioInfo {
  url?: string;
  file_id?: string;
  duration?: Double;
  format?: string;
  file_hash?: string;
  vid?: string;
}

export interface DML_API_COMMON_STRUCT_LipSyncConf {
  enable?: boolean;
  audio_vid?: string;
  /** 对口型音频信息 */
  origin_audio?: DML_API_COMMON_STRUCT_LipSyncAudioInfo;
  /** 对口型视频信息 */
  origin_video?: mweb_common.DML_MWEB_COMMON_VideoInfo;
  /** 前端记录的 tts 信息，做同款用，其中服务端会根据 source_type 判断是否将音频送审；source_type 枚举：本地文件 local-file、tts: text-to-speech、音色迁移: voice-conversion */
  tts_info?: string;
  /** 音色信息：生成时由前端传入；如果一开始是本地音色，发布的同款会更新为发布后的音色信息 */
  voice_info?: mweb_common.DML_MWEB_COMMON_VoiceInfo;
  /** 记录音色迁移前的原始音频 */
  audio_before_conversion?: DML_API_COMMON_STRUCT_LipSyncAudioInfo;
}

export interface DML_API_COMMON_STRUCT_LipSyncInfo {
  lip_sync_detection_status?: Int64;
  lip_sync_extra?: string;
  lip_sync_video_url?: string;
  lip_sync_audio_url?: string;
  labcv_task_id?: string;
}

export interface DML_API_COMMON_STRUCT_Livephoto {
  /** 传true代表端上已经做过人脸检测了，服务端无需再做人脸检测 */
  face_checked?: boolean;
}

export interface DML_API_COMMON_STRUCT_MixAudioVideoArtifact {
  /** 用于音视频合成的视频ItemID */
  video_item_id?: string;
  /** 用于音视频合成的音频vid */
  audio_vid?: string;
  /** 音视频合成产物的ItemID */
  item_id?: string;
  /** 音视频合成产物内容 */
  video?: DML_API_COMMON_STRUCT_EffectVideo;
  /** 音视频合成产物是否已发布 */
  has_published?: boolean;
  /** 音视频合成产物发布后的Vimo ItemID */
  published_item_id?: string;
  /** 合成状态码 */
  status?: mweb_common.DML_MWEB_COMMON_MixAudioVideoStatus;
  /** 合成失败时的错误码 */
  fail_code?: string;
}

export interface DML_API_COMMON_STRUCT_MixAudioVideoData {
  artifact_list?: Array<DML_API_COMMON_STRUCT_MixAudioVideoArtifact>;
}

export interface DML_API_COMMON_STRUCT_MLipSyncImageOpt {
  /** 图片对口型 */
  image?: DML_API_COMMON_STRUCT_ImageInfo;
  /** 音频 */
  audio?: mweb_common.DML_MWEB_COMMON_AudioInfo;
  /** 标准模型形象id */
  resource_id_std?: string;
  /** loopy模型形象id */
  resource_id_loopy?: string;
  tts_content?: string;
  /** 从视频中提取音频 */
  audio_video?: DML_API_COMMON_STRUCT_MVideoInfo;
  /** 不传默认是loopy，传standard是标准模式 */
  mode?: string;
  /** 根据该数据字段判断是否需要超分，width 和 height必传，uri 可选、目前不会在服务端使用 */
  face_image?: DML_API_COMMON_STRUCT_ImageInfo;
  seed?: string;
}

export interface DML_API_COMMON_STRUCT_ModelCommercialConfig {
  commerce_info_map?: Record<string, DML_API_COMMON_STRUCT_CommerceReqInfo>;
}

/** 模型配置 */
export interface DML_API_COMMON_STRUCT_ModelConfig {
  /** 模型图标 */
  icon_url?: string;
  /** 模型名的starling key */
  model_name_starling_key?: string;
  /** 模型使用提示的starling key */
  model_tip_starling_key?: string;
  /** 智创的模型reqkey、视频模型的 model_key */
  model_req_key?: string;
  /** 废弃，不再使用
前端新模型角标 */
  is_new_model?: boolean;
  /** 模型默认精细度 */
  sample_steps?: DML_API_COMMON_STRUCT_ModelSampleSteps;
  /** 废弃，不再使用
支持的垫图能力 */
  blend_enable?: DML_API_COMMON_STRUCT_BlendEnable;
  /** 模型维度的特征列表
详细参考文档《模型feats定义》这里
链接：https://bytedance.larkoffice.com/docx/InG1dVk1KohG4MxT3jGcfSRjnbZ#part-GphndcCSKofJSixv0vRczocpntb */
  feats?: Array<string>;
  /** 模型名 */
  model_name?: string;
  /** 模型描述 */
  model_tip?: string;
  /** 视频场景：通用白名单对应的feature_key，如果为空则模型无需校验，公开可用 */
  feature_key?: string;
  /** 视频模型配置
ai视频可选时长 */
  duration_option?: Array<Int>;
  /** ai视频镜头运动方式 */
  lens_motion_type_option?: Array<string>;
  /** ai视频运动强度 */
  motion_speed_option?: Array<string>;
  /** ai视频尾帧控制 */
  camera_strength_option?: Array<string>;
  /** 比例 '16:9' */
  video_aspect_ratio_option?: Array<string>;
  /** 模型商业化配置 */
  commercial_config?: DML_API_COMMON_STRUCT_ModelCommercialConfig;
  /** 模型默认的视频fps */
  fps?: Int;
  /** feat 参数 */
  feat_config?: Record<string, DML_API_COMMON_STRUCT_ModelFeatParam>;
  extra?: DML_API_COMMON_STRUCT_ModelConfigExtra;
  /** 不能同时选用的feats */
  feats_cant_combine?: Array<Array<string>>;
  /** 底纹词starling key */
  model_bg_prompt_starling_key?: string;
}

export interface DML_API_COMMON_STRUCT_ModelConfigExtra {
  /** ModelVisable告知端上用户目前是否可用，不可用时触发
存量模型不会包含该字段 */
  model_visable?: DML_API_COMMON_STRUCT_ModelVisable;
  /** ug活动配置 */
  ug_act_config?: DML_API_COMMON_STRUCT_UGActConfig;
}

export interface DML_API_COMMON_STRUCT_ModelFeatParam {
  strength?: Double;
}

export interface DML_API_COMMON_STRUCT_ModelInfo {
  icon_url?: string;
  /** 模型名的starling key */
  model_name_starling_key?: string;
  /** 模型使用提示的starling key */
  model_tip_starling_key?: string;
  /** 智创的模型reqkey、视频模型的 model_key */
  model_req_key?: string;
  /** 模型名 */
  model_name?: string;
  /** 模型商业化配置 */
  commercial_config?: DML_API_COMMON_STRUCT_ModelCommercialConfig;
}

/** 模型配置 https://bytedance.larkoffice.com/docx/U1vOdhewGoAhMvxMMRwc1xKxn7M#IHSSdLywjot7TVxvzNkcfrrlnoE */
export interface DML_API_COMMON_STRUCT_ModelOption {
  /** 模型配置项的key，对齐生视频接口的字段，当前有duration_ms、fps、video_mode、camera_strength、input_media_type */
  key?: string;
  /** 配置的数据类型，enum（枚举项）、slide_bar（滑杆） */
  value_type?: string;
  /** 枚举项的可选值，value_type为enum的时候取这个字段 */
  enum_val?: DML_API_COMMON_STRUCT_OptionEnumValue;
  /** 滑杆的取值和步长，value_type为slide_bar的时候取这个字段 */
  slide_bar_val?: DML_API_COMMON_STRUCT_OptionSlideBarValue;
  /** 屏蔽配置项的展示，这时候端上取配置的默认值传上来就可以（这个字段只是控制端上配置ui的显隐，不是不传这个配置） */
  forbidden_display?: boolean;
}

/** 模型精细度配置 */
export interface DML_API_COMMON_STRUCT_ModelSampleSteps {
  /** 默认步数 */
  steps?: Int;
  /** 支持的最小步数，如果小于该步数，会报参数错误 */
  min_steps?: Int;
  /** 支持的最大部署，如果大于等于该步数，会报参数错误 */
  max_steps?: Int;
}

export interface DML_API_COMMON_STRUCT_MSceneVideoUrl {
  download_url?: string;
}

export interface DML_API_COMMON_STRUCT_MVideoInfo {
  /** vid */
  vid?: string;
  /** 帧率 */
  fps?: Int;
  /** 宽 */
  width?: Int;
  /** 高 */
  height?: Int;
  /** 时长 */
  duration?: Int;
  /** 视频的url 页面预览 */
  video_url?: string;
  /** aigc_flow: 视频的一些link信息 */
  link?: string;
  /** 不同场景的URL */
  scene_video_urls?: DML_API_COMMON_STRUCT_MSceneVideoUrl;
  /** 视频封面 */
  cover_url?: string;
  /** 视频格式，mp4、mov */
  format?: string;
  definition?: string;
  /** 水印类型 */
  logo_type?: string;
  encryption_key?: string;
  /** 视频md5 */
  md5?: string;
  /** 视频大小，单位byte */
  size?: Int;
  /** 视频vid */
  video_id?: string;
}

export interface DML_API_COMMON_STRUCT_OptionEnumValue {
  /** 选项的数据类型，当前有string、double、int */
  enum_type?: string;
  /** enum_type为string的时候，取string_val作为可选的配置 */
  string_value?: Array<string>;
  /** enum_type为double的时候，取double_value作为可选的配 */
  double_value?: Array<Double>;
  /** enum_type为int的时候，取int_value作为可选的配 */
  int_value?: Array<Int>;
  /** 默认值的下标，-1代表无默认值 */
  default_val_idx?: Int;
}

export interface DML_API_COMMON_STRUCT_OptionSlideBarValue {
  /** 最小值 */
  min?: Int;
  /** 最大值 */
  max?: Int;
  /** 步长 */
  step?: Int;
  /** 默认值 */
  default?: Int;
}

export interface DML_API_COMMON_STRUCT_OriginItem {
  image_url?: string;
  width?: Int;
  height?: Int;
  uri?: string;
  format?: string;
  /** 是不是父级item */
  is_origin_item?: boolean;
}

export interface DML_API_COMMON_STRUCT_RealmanAvatar {
  enable?: boolean;
  resource_id_std?: string;
  resource_id_loopy?: string;
  origin_image?: DML_API_COMMON_STRUCT_ImageInfo;
  origin_audio?: DML_API_COMMON_STRUCT_LipSyncAudioInfo;
  /** 前端记录的 tts 信息，做同款用，其中服务端会根据 source_type 判断是否将音频送审；source_type 枚举：本地文件 local-file、tts: text-to-speech、音色迁移: voice-conversion */
  tts_info?: string;
  /** 音色信息：生成时由前端传入；如果一开始是本地音色，发布的同款会更新为发布后的音色信息 */
  voice_info?: mweb_common.DML_MWEB_COMMON_VoiceInfo;
  /** 记录音色迁移前的原始音频 */
  audio_before_conversion?: DML_API_COMMON_STRUCT_LipSyncAudioInfo;
}

export interface DML_API_COMMON_STRUCT_RefHistoryInfo {
  /** history_id */
  history_record_id?: string;
  item_id?: string;
  /** 图片信息，上传的时候上传uri，下发的时候服务端实时下发链接 */
  image_info?: mweb_common.DML_MWEB_COMMON_ImageInfo;
  prompt?: string;
}

export interface DML_API_COMMON_STRUCT_ReviewInfo {
  review_status?: Int64;
  review_code_list?: Array<Int>;
  extra_reason?: string;
}

export interface DML_API_COMMON_STRUCT_ShortVideoData {
  ai_tools?: string;
  story_id?: string;
}

export interface DML_API_COMMON_STRUCT_Statistic {
  usage_num?: Int;
  favorite_num?: Int64;
  has_favorited?: boolean;
  feedback_status?: DML_API_COMMON_STRUCT_FeedBack;
  /** 视频播放次数 */
  play_num?: Int64;
  /** 作品评论总数 */
  comment_num?: Int64;
  /** 作品分享总数 */
  share_num?: Int64;
}

export interface DML_API_COMMON_STRUCT_StyleReference {
  /** 风格迁移强度,前端默认值为0.4,范围待定 */
  style_weight?: Double;
  /** 被参考图，会下发多尺寸图片 */
  image?: DML_API_COMMON_STRUCT_ImageInfo;
  /** 目的为了让前端的生图记录可以包含vimo的存储信息 并能通过拉取信息进行面板的恢复
风格存储vimo的item_id */
  style_item_id?: string;
  /** 风格存储vimo的item_title */
  style_title?: string;
  style_title_starling_key?: string;
  /** 风格类型 */
  style_type?: mweb_common.DML_MWEB_COMMON_StyleType;
}

export interface DML_API_COMMON_STRUCT_SuperResolutionConf {
  enable?: boolean;
  /** 目标宽 */
  target_width?: Int64;
  /** 目标高 */
  target_height?: Int64;
  origin_width?: Int64;
  origin_heigth?: Int64;
}

export interface DML_API_COMMON_STRUCT_Tag {
  id?: Int64;
  name?: string;
  tag_id?: string;
}

export interface DML_API_COMMON_STRUCT_TaskPayload {
  task_extra?: string;
  task_scene?: string;
  /** 预估生成需要花费的总时间，单位是s */
  will_cost?: Double;
  /** 预估生成耗时 - 单位:s */
  forecast_generate_cost?: Int64;
  /** 预估排队耗时 - 单位:s */
  forecast_queue_cost?: Int64;
  /** 错误码 */
  fail_code?: string;
  fail_starling_key?: string;
  fail_starling_message?: string;
  debug_info?: mweb_common.DML_MWEB_COMMON_GenerateDebugInfo;
  item_id?: string;
}

/** TaskProcessRecord 视频任务处理过程 */
export interface DML_API_COMMON_STRUCT_TaskProcessRecord {
  task_id?: string;
  cur_process_flows?: Array<DML_API_COMMON_STRUCT_VProcessType>;
  history_id?: string;
}

export interface DML_API_COMMON_STRUCT_Text2VideoMetaData {
  /** 生成的原始输入 */
  video_gen_inputs?: Array<DML_API_COMMON_STRUCT_VideoGenInput>;
  /** 生成视频宽高比，对齐下游算法输入类型
使用字符串存储，例如"16:9"。如果提供了首帧图，则取首帧图宽高比
当前支持的比例："1:1", "3:4", "4:3", "9:16", "16:9" */
  video_aspect_ratio?: string;
  /** uint32 类型的数字，[1, 21亿] */
  seed?: Int64;
  /** 生视频任务场景，"lip_sync"/"insert_frame"/"super_resolution" */
  task_scene?: string;
  /** 任务优先级 */
  priority?: DML_API_COMMON_STRUCT_TaskPriority;
  /** video_gen_inputs的额外参数，如多尺寸图等，通过下标对应 */
  video_gen_inputs_extra?: Array<DML_API_COMMON_STRUCT_VideoGenInputExtra>;
  /** 用户使用的模型配置 */
  model_req_key?: string;
  model_config?: DML_API_COMMON_STRUCT_ModelConfig;
  video_model_config?: DML_API_COMMON_STRUCT_VideoModelConfig;
}

export interface DML_API_COMMON_STRUCT_Text2VideoParams {
  video_gen_inputs?: Array<DML_API_COMMON_STRUCT_VideoGenInput>;
  /** 生成视频宽高比，对齐下游算法输入类型
使用字符串存储，例如"16:9"。如果提供了首帧图，则取首帧图宽高比
当前支持的比例："1:1", "3:4", "4:3", "9:16", "16:9" */
  video_aspect_ratio?: string;
  /** uint32 类型的数字，[1, 21亿] */
  seed?: Int64;
  /** 生视频任务场景，"lip_sync"/"insert_frame"/"super_resolution" */
  task_scene?: string;
  /** 任务优先级 */
  priority?: DML_API_COMMON_STRUCT_TaskPriority;
  /** model config */
  model_req_key?: string;
  model_config?: DML_API_COMMON_STRUCT_ModelConfig;
  video_model_config?: DML_API_COMMON_STRUCT_VideoModelConfig;
}

export interface DML_API_COMMON_STRUCT_Thumb {
  /** 雪碧图详细数据 */
  detail_infos?: Array<DML_API_COMMON_STRUCT_ThumbDetailInfo>;
  /** 雪碧图通用数据 */
  thumb_common_info?: DML_API_COMMON_STRUCT_ThumbCommonInfo;
}

export interface DML_API_COMMON_STRUCT_ThumbCommonInfo {
  /** 单张小图宽度 */
  single_frame_width?: Int;
  /** 单张小图高度 */
  single_frame_height?: Int;
  /** 所有小图数量 */
  total_set_num?: Int;
}

export interface DML_API_COMMON_STRUCT_ThumbDetailInfo {
  /** 长图中包含的小图数量 */
  frame_count?: Int;
  /** 长图的宽度 */
  image_width?: Int;
  /** 长图的高度 */
  image_height?: Int;
  /** 长图uri */
  uri?: string;
  /** 长图url */
  url?: string;
  /** 雪碧图格式，可选值，"image/webp"、"image/jepg" */
  mime_type?: string;
}

/** 直接透传 */
export interface DML_API_COMMON_STRUCT_UGActConfig {
  /** 是否可以展示 */
  can_show?: boolean;
  /** 入口标签 */
  entrance_material?: DML_API_COMMON_STRUCT_EntranceMaterial;
  campaign_key?: string;
  sub_campaign_key?: string;
}

export interface DML_API_COMMON_STRUCT_User {
  name?: string;
  avatar_url?: string;
  uid?: string;
  sec_uid?: string;
  /** 用户简介 */
  description?: string;
  /** 请求uid是否已经关注目标uid */
  has_followed?: boolean;
  /** 目标uid是否关注请求uid */
  followed_req_uid?: boolean;
  /** 账户状态: 0 正常, 1: 取消中, 2: 已注销 */
  status?: Int;
  /** 账号类型: 1 正常 2 测试 */
  account_type?: DML_API_COMMON_STRUCT_AccountType;
  work_allowed_share?: boolean;
}

export interface DML_API_COMMON_STRUCT_V2VOpt {
  /** 视频延长3s */
  extend?: DML_API_COMMON_STRUCT_ExtendConf;
  /** 插帧 */
  insert_frame?: DML_API_COMMON_STRUCT_InsertFrameConf;
  /** 超分 */
  super_resolution?: DML_API_COMMON_STRUCT_SuperResolutionConf;
  /** AIGC 生成视频对口型 */
  lip_sync?: DML_API_COMMON_STRUCT_LipSyncConf;
  /** lab 的预览视频进行超分 */
  lab_sr?: DML_API_COMMON_STRUCT_LabSrConf;
  /** 音视频合成 */
  av_mix?: DML_API_COMMON_STRUCT_AudioVideoMixConf;
  /** 用户上传视频对口型 */
  lip_sync_user_video?: DML_API_COMMON_STRUCT_LipSyncConf;
  /** 视频模板 */
  video_template?: DML_API_COMMON_STRUCT_VideoTemplateConf;
}

export interface DML_API_COMMON_STRUCT_VideoAudioEffectData {
  result_list?: Array<DML_API_COMMON_STRUCT_VideoAudioEffectResult>;
  default_audio?: DML_API_COMMON_STRUCT_EffectAudio;
  default_audio_idx?: Int;
}

export interface DML_API_COMMON_STRUCT_VideoAudioEffectResult {
  /** 生成的音效 */
  audio?: DML_API_COMMON_STRUCT_EffectAudio;
  /** 音视频合成的结果 */
  mixed_video?: DML_API_COMMON_STRUCT_MixAudioVideoArtifact;
  /** 音效生成失败时，表示失败的细分错误码 */
  fail_code?: string;
  /** 生成状态 */
  generate_status?: mweb_common.DML_MWEB_COMMON_GenerateAudioEffectStatus;
}

export interface DML_API_COMMON_STRUCT_VideoBGMData {
  bgm_list?: Array<DML_API_COMMON_STRUCT_EffectAudio>;
  default_bgm?: DML_API_COMMON_STRUCT_EffectAudio;
}

export interface DML_API_COMMON_STRUCT_VideoGenInput {
  prompt?: string;
  /** 首帧图片 */
  first_frame_image?: DML_API_COMMON_STRUCT_ImageInfo;
  /** 尾帧图片 */
  end_frame_image?: DML_API_COMMON_STRUCT_ImageInfo;
  /** ai视频镜头运动方式，Still：随机运镜，ZoomIn：推进，ZoomOut：拉远，RotateClockwise：顺时针旋转，RotateAnticlockwise：逆时针旋转
[Still，PanLeft, PanRight, TiltUp, TiltDown, HorizontalLeft, HorizontalRight, VerticalUp, VerticalDown, ZoomIn, ZoomOut, RotateClockwise, RotateAnticlockwise] */
  lens_motion_type?: string;
  /** ai视频运动强度，Low：慢速，Moderate：适中，High：快速 */
  motion_speed?: string;
  /** 当需要延长某个视频时, 可以传这个字段 */
  vid?: string;
  /** ai视频尾帧控制，默认传"1.0" */
  ending_control?: string;
  /** v2v场景，可以传入这个字段, string */
  pre_task_id?: string;
  audio_vid?: string;
  v2v_opt?: DML_API_COMMON_STRUCT_V2VOpt;
  video_mode?: DML_API_COMMON_STRUCT_VideoMode;
  fps?: Int;
  /** 视频生成时表示生成多长的视频 */
  duration_ms?: Int;
  /** 运镜运动幅度,仅在 AILABV2_DEFAULT/AILABV2_BASE 中有效 0,1,2 */
  camera_strength?: string;
  boximator?: DML_API_COMMON_STRUCT_Boximator;
  /** 做同款场景，用到的模板id，类型是字符串 */
  template_id?: string;
  i2v_opt?: DML_API_COMMON_STRUCT_I2VOpt;
  /** 引用的history信息，服务端做透传，下发的时候会填充图片的信息 */
  ref_history_info?: DML_API_COMMON_STRUCT_RefHistoryInfo;
}

export interface DML_API_COMMON_STRUCT_VideoGenInputExtra {
  /** VideoGenInput.first_frame_image的多尺寸图 */
  multi_first_frame_image?: Array<DML_API_COMMON_STRUCT_ImageInfo>;
  /** VideoGenInput.end_frame_image的多尺寸图 */
  multi_end_frame_image?: Array<DML_API_COMMON_STRUCT_ImageInfo>;
}

/** https://bytedance.larkoffice.com/docx/U1vOdhewGoAhMvxMMRwc1xKxn7M#GRp7d7YuXo4FotxFjRvcqdDSn9H */
export interface DML_API_COMMON_STRUCT_VideoModelCommercialConfig {
  /** 默认的商业化key */
  default?: DML_API_COMMON_STRUCT_CommerceReqInfo;
  /** 这个字段如果存在，说明商业化的key需要通过参数来计算。比如format为{fps}，说明需要通过用户选中的fps的值，从format_conf取具体的商业化配置 */
  format?: string;
  format_conf?: Record<string, DML_API_COMMON_STRUCT_CommerceReqInfo>;
}

/** 模型配置 */
export interface DML_API_COMMON_STRUCT_VideoModelConfig {
  /** 模型图标 */
  icon?: DML_API_COMMON_STRUCT_ImageInfo;
  /** 模型名 */
  model_name?: string;
  model_name_starling_key?: string;
  /** 模型描述 */
  model_tip?: string;
  model_tip_starling_key?: string;
  /** 视频场景：通用白名单对应的feature_key，如果为空则模型无需校验，公开可用 */
  feature_key?: string;
  /** 模型标签，new、beta，空代表无标签 */
  icon_tag?: string;
  /** 视频模型的配置项 */
  options?: Array<DML_API_COMMON_STRUCT_ModelOption>;
  /** 商业化配置 */
  commercial_config?: DML_API_COMMON_STRUCT_VideoModelCommercialConfig;
  /** 模型的req key */
  model_req_key?: string;
  /** 模型的额外信息 */
  extra?: DML_API_COMMON_STRUCT_VideoModelConfigExtra;
  model_status?: DML_API_COMMON_STRUCT_VideoModelStatus;
}

/** VideoModelConfigExtra 存放模型的一些额外信息 */
export interface DML_API_COMMON_STRUCT_VideoModelConfigExtra {
  /** 内测弹窗的底图，因为和模型挂钩，选择放在服务端管理 */
  pop_icon?: DML_API_COMMON_STRUCT_ImageInfo;
  /** 模型来源 */
  model_source?: string;
  /** ModelVisable告知端上用户目前是否可用，不可用时触发
存量模型不会包含该字段 */
  model_visable?: DML_API_COMMON_STRUCT_ModelVisable;
  /** ug活动配置 */
  ug_act_config?: DML_API_COMMON_STRUCT_UGActConfig;
}

export interface DML_API_COMMON_STRUCT_VideoTemplateConf {
  enable?: boolean;
  /** 视频模板对应的视频ItemID */
  template_id?: string;
  image_info?: DML_API_COMMON_STRUCT_ImageInfo;
  /** 支持 VID */
  video_info?: mweb_common.DML_MWEB_COMMON_VideoInfo;
}

export interface DML_API_COMMON_STRUCT_VolumeInfo {
  /** 响度 */
  loudness?: Double;
  /** 最大音量 */
  peak?: Double;
}
/* eslint-enable */
