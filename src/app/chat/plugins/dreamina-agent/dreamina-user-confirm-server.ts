import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import {
    NuroMCPClientAdapterImpl,
    NuroMCPServerConfig,
    transformToZodSchema
} from "@byted/nurosdk-js";
import {getAllToolServer} from "@byted/dreamina_dml_js_web";
import Zod from "zod";




export function getToolServer():NuroMCPServerConfig[]{
    let servers:NuroMCPServerConfig[] = []
    let ts = getAllToolServer()
    ts.forEach(tServer=>{
        let mcpServer = new McpServer({
            name: tServer.name,
            version: tServer.version,
        });

        tServer.tools.forEach(toolDefine=>{
            mcpServer.tool(
                toolDefine.name,
                toolDefine.description,
                transformToZodSchema(Zod,toolDefine.inputSchema),
                async (inputs) => {
                    return { content: [] };
                }
            )
        })
       let  adapterImpl  = new NuroMCPClientAdapterImpl(
            tServer.name,
           mcpServer
        )
        let serverConfig = new NuroMCPServerConfig(tServer.name, adapterImpl)
        servers.push(serverConfig)

    })
    return servers
}


