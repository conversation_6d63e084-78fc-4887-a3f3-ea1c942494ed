import { Utils } from "../../common/utils";
import {
  DML_API_COMMON_STRUCT_AigcData,
  DML_API_COMMON_STRUCT_GenerateTaskStatus,
} from "./bam/api-common-struct";
import { Axios } from "axios";
import { initializeHostAgentOptions } from "../../utils/hostAgentOptionsHelper";
import {
  DAAIGCMode,
  DADraft,
  DAGenCoreParam,
  DAImageBaseComponent,
  DAImageBlendAbility,
  DAImageGenerateAbilities,
  DAImageGenerateAbility,
  DAImageGenerateType,
  DAImageRatioType,
} from "@byted/dreamina_agreement_js";

const appId = "581595";

const commonHeaders = {
  Accept: "application/json, text/plain, */*",
  "User-Agent":
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  "sec-ch-ua-platform": "macOS",
  "sec-ch-ua":
    '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
  Referer: "https://jimeng.jianying.com/ai-tool/image/generate",
  pf: "7",
  "sec-ch-ua-mobile": "?0",
  "sign-ver": "1",
  appvr: "5.8.0",
};

type Text2ImageProps = {
  prompt: string;
  ratio?: string;
  image_tags?: string[];
};

export class DreaminaAgentService {
  static async submitText2ImageGenerateRequest(
    props: Text2ImageProps
  ): Promise<string> {
    let draft = new DADraft();
    let coreParam: DAGenCoreParam = (() => {
      let coreParam = new DAGenCoreParam();
      coreParam.prompt = props.prompt;
      coreParam.seed = Math.floor(Math.random() * 2100000000);
      coreParam.model = "high_aes_general_v30l:general_v3.0_18b";
      coreParam.sampleStrength = 0.5;
      coreParam.imageRatio = convertToImageRatioTypeFromString(
        props.ratio ?? "1:1"
      );
      return coreParam;
    })();
    let imageGenerateAbility = new DAImageGenerateAbility();
    imageGenerateAbility.coreParam = coreParam;
    let imageBaseComponent = new DAImageBaseComponent();
    imageBaseComponent.generateType = DAImageGenerateType.generate;
    imageBaseComponent.aigcMode = DAAIGCMode.workbench;
    imageBaseComponent.abilities = (() => {
      let abilities = new DAImageGenerateAbilities();
      abilities.generate = imageGenerateAbility;
      return abilities;
    })();
    draft.setMainComponent(imageBaseComponent);
    let draftContent = draft.toJSONString()!;
    return await new GenerateTask(draftContent).run(coreParam.model ?? "");
  }

  static async loopGetGenerateResult(
    submitId: string
  ): Promise<DML_API_COMMON_STRUCT_AigcData | undefined> {
    const hostAgentOptions = initializeHostAgentOptions();
    const loopResultUri =
      "https://jimeng.jianying.com/mweb/v1/get_history_by_ids?aid=513695";
    let deviceTime = new Date().getTime() / 1000;
    const sign = Utils.md5(
      `9e2c|${loopResultUri.slice(-7)}|7|5.8.0|${deviceTime}||11ac`
    );
    const loopResultResponse = await new Axios({}).post(
      "/api/chat_proxy",
      JSON.stringify({
        submit_ids: [submitId],
      }),
      {
        headers: {
          "x-url": loopResultUri,
          "Content-Type": "application/json",
          "device-time": deviceTime,
          sign: sign,
          ...hostAgentOptions,
          ...commonHeaders,
        },
        responseType: "text",
      }
    );
    const queryResult = JSON.parse(JSON.parse(loopResultResponse.data)).data?.[
      submitId
    ] as DML_API_COMMON_STRUCT_AigcData | undefined;
    if (
      queryResult?.status ===
      DML_API_COMMON_STRUCT_GenerateTaskStatus.FinalSuccess
    ) {
      return queryResult;
    }
  }

  static async loopGetGenerateResultByHistoryId(
    historyId: string
  ): Promise<DML_API_COMMON_STRUCT_AigcData | undefined> {
    const hostAgentOptions = initializeHostAgentOptions();
    const loopResultUri =
      "https://jimeng.jianying.com/mweb/v1/get_history_by_ids?aid=513695";
    let deviceTime = new Date().getTime() / 1000;
    const sign = Utils.md5(
      `9e2c|${loopResultUri.slice(-7)}|7|5.8.0|${deviceTime}||11ac`
    );
    const loopResultResponse = await new Axios({}).post(
      "/api/chat_proxy",
      JSON.stringify({
        history_ids: [historyId],
      }),
      {
        headers: {
          "x-url": loopResultUri,
          "Content-Type": "application/json",
          "device-time": deviceTime,
          sign: sign,
          ...hostAgentOptions,
          ...commonHeaders,
        },
        responseType: "text",
      }
    );
    const queryResult = JSON.parse(JSON.parse(loopResultResponse.data)).data?.[
      historyId
    ] as DML_API_COMMON_STRUCT_AigcData | undefined;
    if (
      queryResult?.status ===
      DML_API_COMMON_STRUCT_GenerateTaskStatus.FinalSuccess
    ) {
      return queryResult;
    }
  }

}

class GenerateTask {
  constructor(private readonly draftContent: string) {}

  async run(modelName: string): Promise<string> {
    const hostAgentOptions = initializeHostAgentOptions();
    let submitId = `${appId}_${Utils.randomUUIDString()}`; // uuid
    let draftContent = this.draftContent;
    let axios = new Axios({});
    const uri =
      "https://jimeng.jianying.com/mweb/v1/aigc_draft/generate?aid=513695";
    const deviceTime = Date.now().toString();
    const sign = Utils.md5(`9e2c|${uri.slice(-7)}|7|5.8.0|${deviceTime}||11ac`);
    const submitResponse = await axios.post(
      "/api/chat_proxy",
      JSON.stringify({
        submit_id: submitId,
        draft_content: draftContent,
        extend: {
          root_model: modelName,
        },
      }),
      {
        headers: {
          "x-url": uri,
          "Content-Type": "application/json",
          "device-time": deviceTime,
          sign: sign,
          ...hostAgentOptions,
          ...commonHeaders,
        },
        responseType: "text",
      }
    );
    const submitData = JSON.parse(JSON.parse(submitResponse.data));
    if (submitData.ret !== "0") {
      console.log(
        "submitResponse: ",
        JSON.parse(JSON.parse(submitResponse.data))
      );
      throw "生成失败，任务提交失败。";
    }
    return submitId;
  }
}

const convertToImageRatioTypeFromString = (value: string): DAImageRatioType => {
  switch (value) {
    case "1:1":
      return DAImageRatioType.ImageRatioType_11;
    case "3:4":
      return DAImageRatioType.ImageRatioType_34;
    case "16:9":
      return DAImageRatioType.ImageRatioType_169;
    case "4:3":
      return DAImageRatioType.ImageRatioType_43;
    case "9:16":
      return DAImageRatioType.ImageRatioType_916;
    case "2:3":
      return DAImageRatioType.ImageRatioType_23;
    case "3:2":
      return DAImageRatioType.ImageRatioType_32;
    case "21:9":
      return DAImageRatioType.ImageRatioType_219;
    default:
      return DAImageRatioType.ImageRatioType_11;
  }
};
