import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  NuroToolCallMessage,
} from "@byted/nurosdk-js"; // Added import
import {
  image2imageName,
  image2imageV3Name,
  image2videoName,
  image2videoV3Name,
  localText2imageName,
  pointsConsumptionReminderName, start2EndVideoV3Name, text2imageFormName,
  text2imageName,
  text2imageV3Name,
  text2videoName,
  text2videoV3Name
} from "@byted/dreamina_dml_js_web"; // Added import
import {
  Plugin,
  PluginCapability,
  ToolCallCellRendererProps,
} from "../../plugin-system/plugin-types";
import {getToolServer} from "./dreamina-user-confirm-server";
import { UserConfirmText2ImageFormCell } from "./cell/user-confirm-text2-image-form-cell"; // Added import
import { LoacalGenerateText2ImageCell } from "./cell/local-generate-text2-image-cell";
import { GenerateSingleVideoCell } from "./cell/generate-single-video-cell";
import { PointReminderCell } from "./cell/point-reminder-cell";
import { GenerateText2ImageCellWithReference} from "./cell/generate-text2-image-cell";
import {NuroAssistantMessage, NuroUserMessage} from "@byted/nurosdk-js";
import React from 'react';

export  class WebDemoTaskChecker extends NuroTaskChecker {

  /**
   * 产物消息
   * @param message
   */
  isArtifactMessage(message: NuroMessage): boolean {

    let sp = super.isArtifactMessage(message);
    if(sp === true){
      return true
    }

    if (message instanceof NuroAssistantMessage) {
      return message.relateToolCalls.length === 0
    }

    return false;
  }
}


const DreaminaAgentPlugin: Plugin = {
  id: "dreamina_agent",
  name: "Dreamina Agent",
  description: "对应「即梦」线上的 Agent 功能，用于产品 Demo 演示和调试。",
  version: "2.0",
  capabilities: {
    [PluginCapability.CustomTaskChecker]: () => {
      return new WebDemoTaskChecker();
    },
    [PluginCapability.CustomMCPServer]: () => {
      return getToolServer()
    },
    [PluginCapability.CustomToolCallCell]: (
      message: NuroToolCallMessage
    ): React.FC<ToolCallCellRendererProps> | undefined => {
      if (message.toolName === text2imageFormName) {
        return UserConfirmText2ImageFormCell;
      }
      if (message.toolName === localText2imageName ) {
        return LoacalGenerateText2ImageCell;
      }
      if (message.toolName === text2imageName || message.toolName === image2imageName || message.toolName === text2imageV3Name || message.toolName === image2imageV3Name) {
        return GenerateText2ImageCellWithReference;
      }
      else if (message.toolName === text2videoName || message.toolName === image2videoName || message.toolName === text2videoV3Name || message.toolName === image2videoV3Name || message.toolName === start2EndVideoV3Name) {
        return GenerateSingleVideoCell;
      }
      else if (message.toolName === pointsConsumptionReminderName) {
        return PointReminderCell;
      }
      return undefined;
    },
  },
};

export default DreaminaAgentPlugin;
