import React, { useEffect } from "react";
import { ToolCallCellRendererProps } from "../../../plugin-system/plugin-types";
import {
  EventStreamAdapter,
  NuroToolCallMessage,
  NuroUserMessage,
  NuroUtils,
} from "@byted/nurosdk-js";
import { DreaminaAgentService } from "../dreamina-agent-service";
import { DML_API_COMMON_STRUCT_AigcData } from "../bam/api-common-struct";

interface Text2ImageArgs {
  title?: string;
  prompt?: string;
  ratio?: string;
  instruction?: string;
  image_tags?: string[];
}

// The toolResult for text2image_form contains the inputs again in a resource object
// For this cell, we are primarily interested in displaying the arguments (inputs) passed to the tool.

export const LoacalGenerateText2ImageCell: React.FC<ToolCallCellRendererProps> = ({
  message,
}) => {
  const [submitId, setSubmitId] = React.useState<string | undefined>();
  const [aigcData, setAigcData] = React.useState<
    DML_API_COMMON_STRUCT_AigcData | undefined
  >(undefined);

  let toolArgs: Text2ImageArgs = {};
  try {
    // toolArgs are expected to be a JSON string of Text2ImageFormArgs
    if (message.toolArgs) {
      toolArgs = JSON.parse(message.toolArgs);
    }
  } catch (e) {
    console.error("Error parsing text2image_form tool args:", e);
  }

  useEffect(() => {
    async function loopGetResponse(submitId: string) {
      console.log("loopGetResponse", submitId);
      const result = await DreaminaAgentService.loopGetGenerateResult(
        submitId
      );
      console.log("loopGetResponse result", submitId, result);
      if (!result) {
        await new Promise((resolve) => setTimeout(resolve, 2000));
        loopGetResponse(submitId);
        return;
      } else {
        setAigcData(result);
      }
    }

    if (message instanceof NuroToolCallMessage) {
      if (submitId) {
        return;
      }
      let payload: any;
      try {
        payload = JSON.parse(message.metadata.payload ?? "");
      } catch (error) {
        payload = {};
      }
      console.log("payload", payload);
      if (payload && payload.submitId) {
        // 已经提交过草稿了，顺询任务即可。
        loopGetResponse(payload.submitId);
      } else {
        // 没有提交过草稿，直接提交任务。
        if (!toolArgs.prompt) {
          console.error("No prompt provided for text2image_form");
          return;
        }
        DreaminaAgentService.submitText2ImageGenerateRequest({
          prompt: toolArgs.prompt,
          ratio: toolArgs.ratio,
          image_tags: toolArgs.image_tags,
        }).then((submitId) => {
          setSubmitId(submitId);
          EventStreamAdapter.payloadEndpoint = "https://jimeng.jianying.com/mweb/v1/creation_agent/v2/set_message_payload"
          message.setMessagePayload(
            JSON.stringify({ submitId }),
            () => {},
            () => {}
          );
          loopGetResponse(submitId);
        });
      }
    }
  }, []);

  const isGenerating = aigcData === undefined;
  const resultImages =
    aigcData?.item_list?.map(
      (item) => item.image?.large_images?.[0]?.image_url
    ) ?? [];

  return (
    <div className="p-4 mb-8 bg-white text-gray-800 rounded-lg shadow-md w-full my-2 border border-gray-200">
      <div className="mb-3">
        <h3 className="text-lg font-semibold text-purple-600">图像生成</h3>
      </div>

      {isGenerating ? (
        <div className="mt-4 pt-3 border-t border-gray-200 flex justify-center items-center">
          <p className="text-sm text-gray-600">正在生成图像，请稍候...</p>
        </div>
      ) : resultImages.length > 0 ? (
        <div className="mt-4 pt-3 border-t border-gray-200">
          <div className="grid grid-cols-2 gap-4">
            {resultImages.map(
              (imageUrl, index) =>
                imageUrl && (
                  <img
                    key={index}
                    src={imageUrl}
                    alt={`Generated Image ${index + 1}`}
                    className="w-full h-auto rounded-lg shadow-md"
                  />
                )
            )}
          </div>
        </div>
      ) : (
        <div className="mt-4 pt-3 border-t border-gray-200 flex justify-center items-center">
          <p className="text-sm text-gray-600">
            图片生成失败或未找到图片数据。
          </p>
        </div>
      )}
    </div>
  );
};
