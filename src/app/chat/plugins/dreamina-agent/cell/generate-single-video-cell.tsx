import React, { lazy, useEffect } from "react";
import { ToolCallCellRendererProps } from "../../../plugin-system/plugin-types";
import {
  NuroToolCallMessage,
  NuroUserMessage,
  NuroUtils,
} from "@byted/nurosdk-js";
import { DreaminaAgentService } from "../dreamina-agent-service";
import { DML_API_COMMON_STRUCT_AigcData } from "../bam/api-common-struct";
import {
  GenerateVideoToolReq,
  GeneratePushData,
  isInsufficientPoints,
  getGenerateMediaToolResp,
  GenerateExtraData, getGenerateVideoToolReq, getGenerateExtraData, isGeneratePushDataFail
} from "@byted/dreamina_dml_js_web";
const ReactJsonView = lazy(() => import("@microlink/react-json-view"));

export const GenerateSingleVideoCell: React.FC<ToolCallCellRendererProps> = ({
  message,
}) => {
  const [historyId, setHistoryId] = React.useState<string | undefined>();
  const [aigcData, setAigcData] = React.useState<
    DML_API_COMMON_STRUCT_AigcData | undefined
  >(undefined);
  const [videoError, setVideoError] = React.useState(false);

  let toolArgs: GenerateVideoToolReq | undefined = undefined
  try {
    if(message.toolArgs!==undefined ){
      toolArgs = getGenerateVideoToolReq(message.toolArgs);
    }
  } catch (e) {
    console.error("Error parsing GenerateSingleVideoCell tool args:", e);
  }

  let toolExtra: GenerateExtraData | undefined = undefined
  try {
    if(message.toolExtra!==undefined){
      toolExtra = getGenerateExtraData(message.toolExtra);
    }
  } catch (e) {
    console.error("Error parsing GenerateSingleVideoCell tool Extra:", e);
  }
// 假设 GenerateExtraData 类型有 resource_type 属性，添加类型断言确保代码能正常访问
if (toolExtra) {
  const typedToolExtra = toolExtra as { resource_type?: string };
  console.info("GenerateSingleVideoCell toolExtra:", typedToolExtra.resource_type??"");
} else {
  console.info("GenerateSingleVideoCell toolExtra:", "");
}

  let toolResult: GeneratePushData | undefined = undefined
  try {
      if (message.toolResult) {
        console.log("parsing message.toolResult", message.toolResult);
          if(message.toolResult!==undefined && message.toolResult.trim()!== ''){
              toolResult = getGenerateMediaToolResp(message.toolResult);
          }
      }
  } catch (e) {
    console.error("General error processing GenerateSingleVideoCell toolResult:", e);
  }

    // 用于标记 loopGetResponse 是否已经被调用过
    const hasCalledLoopGetResponse = React.useRef(false);

  useEffect(() => {
    console.log("GenerateSingleVideoCell useEffect start");
    async function loopGetResponse(historyId: string) {
      console.log("loopGetResponse", historyId);
      const result = await DreaminaAgentService.loopGetGenerateResultByHistoryId(
        historyId
      );
      console.log("loopGetResponse result", historyId, result);
      if (!result) {
        await new Promise((resolve) => setTimeout(resolve, 2000));
        loopGetResponse(historyId);
        return;
      } else {
        setAigcData(result);
      }
    }

    if (message instanceof NuroToolCallMessage && !hasCalledLoopGetResponse.current) {
      console.log("useEffect historyId:", historyId);
      if (toolResult!== undefined && toolResult.history_id!== undefined) {
        console.log("useEffect  setHistoryId:", toolResult.history_id);
        setHistoryId(toolResult.history_id);
        loopGetResponse(toolResult.history_id);
        hasCalledLoopGetResponse.current = true;
      }
    }
  },  [message, toolResult]); 

  const isInsufficient = isInsufficientPoints(message)
  console.log("GenerateSingleVideoCell isInsufficient:", isInsufficient);
  const isFail:boolean = isGeneratePushDataFail(message)

  const isGenerating = aigcData === undefined;
  console.log("GenerateSingleVideoCell is:", isInsufficient);

  const resultVideo = aigcData?.item_list && aigcData.item_list.length > 0
    ? (() => {
        const transcodedVideo = aigcData.item_list[0].video?.transcoded_video;
        if (transcodedVideo) {
          const firstKey = Object.keys(transcodedVideo)[0];
          if (firstKey) {
            return transcodedVideo[firstKey]?.video_url ?? "";
            // return "https://v6-artist.vlabvod.com/d927ef0e2e12996878f735c5e52efd2f/68583745/video/tos/cn/tos-cn-v-148450/o0gfA8gNMCUhWeScr7QefNAXOEIimpLBujGDlI/?a=4066&ch=0&cr=0&dr=0&er=0&cd=0%7C0%7C0%7C0&br=6724&bt=6724&cs=0&ds=12&ft=5QYTUxhhe6BMyq1cIXkJD12Nzj&mime_type=video_mp4&qs=0&rc=aWY4NDpkOGk8aDo2ZzVmaUBpanY8Zm85cmdpNDczNDM7M0AuMDMyMjNeX18xYTM0X2BjYSMvL2FuMmRjbTRhLS1kNC9zcw%3D%3D&btag=c0000e00008000&dy_q=1750006976&feature_id=7bed9f9dfbb915a044e5d473759ce9df&l=2025061601025662764D0FE680E15340D7";
          }
        }
        return "";
      })()
    : "";
    

  const coverUrl = aigcData?.item_list && aigcData.item_list.length > 0
      ? (() => {
        return aigcData.item_list[0].video?.cover_url??"";
      })()
      : "";

  console.log("GenerateSingleVideoCell coverUrl:", coverUrl);
  //const isGenerating = false;
  //const resultVideo  = "http://v6-artist.vlabvod.com/439970487eda51bd48085cd8060d3591/68512d2b/video/tos/cn/tos-cn-v-148450/ocFIDG8ziiJiGIQQEgZBhlqNf48Ey90HaAqPAq/?a=4066&ch=0&cr=0&dr=0&er=0&cd=0%7C0%7C0%7C0&br=6686&bt=6686&cs=0&ds=12&ft=5QYTUxhhe6BMyqEx.BkJD12Nzj&mime_type=video_mp4&qs=0&rc=NTw7aDtnZjo6ZzhpZ2U4ZUBpanI7ZHc5cjh5NDczNDM7M0A1MTVfXjI1X2MxMF9jMy4xYSNtb2llMmQ0MjBhLS1kNC9zcw%3D%3D&btag=80000e00008000&dy_q=1749545638&feature_id=7bed9f9dfbb915a044e5d473759ce9df&l=20250610165357E9376E1E9E2653D7F5CD.mp4"
  return (
    <div className="p-4 mb-8 bg-white text-gray-800 rounded-lg shadow-md w-full my-2 border border-gray-200">
      <div className="mb-3">
        <h3 className="text-lg font-semibold text-purple-600">视频生成:{message.toolName}</h3>
      </div>
      {message.toolArgs  && (
          <div className="text-gray-400">
          <div style={{ marginBottom: 4 }}>工具调用参数</div>
          <ReactJsonView
            src={(() => {
              try {
                return JSON.parse(message.toolArgs ?? "");
              } catch (error) {
                return {};
              }
            })()}
          />
          </div>
      )}
      {message.toolResult  && (
          <div className="text-gray-400">
          <div style={{ marginBottom: 4 }}>工具调用结果</div>
          <ReactJsonView
            src={(() => {
              try {
                return JSON.parse(message.toolResult ?? "");
              } catch (error) {
                return {};
              }
            })()}
          />
          </div>
      )}
      {isInsufficient ? (
        <div className="mt-4 pt-3 border-t border-gray-200 flex justify-center items-center">
          <p className="text-sm text-red-500">积分不足。</p>
        </div>
      ) : isFail ? (
          <div className="mt-4 pt-3 border-t border-gray-200 flex justify-center items-center">
            <p className="text-sm text-red-500">视频生成失败</p>
          </div>
      ) : isGenerating ? (
        <div className="mt-4 pt-3 border-t border-gray-200 flex justify-center items-center">
          <p className="text-sm text-gray-600">正在生成视频，请稍候...</p>
        </div>
      ) : resultVideo ? (
        <div className="mt-4 pt-3 border-t border-gray-200">
          <video
            autoPlay={true}
            loop={true}
            muted={true}
            src={resultVideo}
            className="w-full h-auto rounded-lg shadow-md"
            onError={(event) => {
              const target = event.target as HTMLVideoElement;
              const error = target.error;
              if (error) {
               console.error('视频加载失败，错误原因:', error.message, ",resultVideo", resultVideo);
              }
             setVideoError(true);
           }}
          />

          {/* <img
              src={coverUrl}
              className="w-full h-auto rounded-lg shadow-md"
              onClick={() => {
                if (resultVideo) {
                  const newWindow = window.open(resultVideo, '_blank');
                }
              }}
          /> */}
          {videoError && (
            <div className="mt-2 flex justify-center items-center">
              <p className="text-sm text-red-500">视频加载失败，请检查视频链接。</p>
            </div>
          )}
        </div>
      ) : (
        <div className="mt-4 pt-3 border-t border-gray-200 flex justify-center items-center">
          <p className="text-sm text-gray-600">
            视频生成失败或未找到视频数据。
          </p>
        </div>
      )}
    </div>
  );
};