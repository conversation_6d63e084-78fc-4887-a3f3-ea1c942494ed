import React, { lazy, useEffect } from "react";
import { ToolCallCellRendererProps } from "../../../plugin-system/plugin-types";
import {
  NuroToolCallMessage,
  NuroFile, // 假设 NuroFile 从这里导入，或者它是一个全局类型
  NuroFileType,
  RefContent,
} from "@byted/nurosdk-js";
import { DreaminaAgentService } from "../dreamina-agent-service";
import { DML_API_COMMON_STRUCT_AigcData } from "../bam/api-common-struct";
import {
  GenerateExtraData,
  getInsertImageToolReq,
  GeneratePushData, 
  getGenerateMediaToolResp,
  isInsufficientPoints,
  getGenerateExtraData,
  isGeneratePushDataFail,
  InsertImageToolReq,
} from "@byted/dreamina_dml_js_web";
const ReactJsonView = lazy(() => import("@microlink/react-json-view"));
import { ImageComponentWithReference } from "./reference-image";

// The toolResult for text2image_form contains the inputs again in a resource object
// For this cell, we are primarily interested in displaying the arguments (inputs) passed to the tool.

export interface InsertText2ImageCellProps {
  onReferenceSingleImage?: (message: RefContent[]) => void;
}

export const InsertText2ImageCell: React.FC<InsertText2ImageCellProps & ToolCallCellRendererProps> = ({
  message,
  onReferenceSingleImage,
}) => {
  const [historyId, setHistoryId] = React.useState<string | undefined>();
  const [aigcData, setAigcData] = React.useState<
    DML_API_COMMON_STRUCT_AigcData | undefined
  >(undefined);

  let toolArgs: InsertImageToolReq | undefined = undefined
  try {
      if(message.toolArgs!==undefined && message.toolArgs.trim()!== ''){
          toolArgs = getInsertImageToolReq(message.toolArgs);
        
      }
  } catch (e) {
    console.error("Error parsing GenerateText2ImageCell tool args:", e);
  }

  let toolExtra: GenerateExtraData | undefined = undefined
  try {
    if(message.toolExtra!==undefined){
      toolExtra = getGenerateExtraData(message.toolExtra);
    }
  } catch (e) {
    console.error("Error parsing GenerateText2ImageCell tool Extra:", e);
  }
// 假设 GenerateExtraData 类型有 resource_type 属性，添加类型断言确保代码能正常访问
if (toolExtra) {
  const typedToolExtra = toolExtra as { resource_type?: string };
  console.info("GenerateSingleVideoCell toolExtra:", typedToolExtra.resource_type??"");
} else {
  console.info("GenerateSingleVideoCell toolExtra:", "");
}

const [toolResult, setToolResult] = React.useState<GeneratePushData | undefined>();

useEffect(() => {
  try {
    if (message.toolResult && message.toolResult.trim() !== '') {
      console.log('[InsertText2ImageCell] parsing tool result '+ message.toolResult);
      const newToolResult = getGenerateMediaToolResp(message.toolResult);
      // Only update state if the new result is different from the current one
      if (JSON.stringify(newToolResult) !== JSON.stringify(toolResult)) {
        setToolResult(newToolResult);
      }
    }
  } catch (e) {
    console.warn("Error parsing GenerateText2ImageCell tool toolResult:", e);
  }
}, [message.toolResult, toolResult]); 

  // 用于标记 loopGetResponse 是否已经被调用过
  const hasCalledLoopGetResponse = React.useRef(false);

  useEffect(() => {
    console.log("GenerateText2ImageCell useEffect start:");
    async function loopGetResponse(historyId: string) {
      console.log("loopGetResponse", historyId);
      const result = await DreaminaAgentService.loopGetGenerateResultByHistoryId(
        historyId
      );
      console.log("loopGetResponse result", historyId, result);
      
      if (!result) {
        await new Promise((resolve) => setTimeout(resolve, 2000));
        loopGetResponse(historyId);
        return;
      } else {
        setAigcData(result);
      }
    }

    if (message instanceof NuroToolCallMessage && !hasCalledLoopGetResponse.current) {
      console.log("useEffect historyId:", historyId);
      if (toolResult!== undefined && toolResult.history_id!== undefined) {
        console.log("useEffect  setHistoryId:", toolResult.history_id);
        setHistoryId(toolResult.history_id);
        loopGetResponse(toolResult.history_id);
        hasCalledLoopGetResponse.current = true;
      }
    }
  }, [message, toolResult]); // 将 message 和 toolResult 作为依赖项，确保 toolResult 赋值后再使用

  const isInsufficient = isInsufficientPoints(message)
  const isFail:boolean = isGeneratePushDataFail(message)
  const isGenerating = aigcData === undefined;
  const resultImages =
    aigcData?.item_list?.map(
      (item) => ({imageUrl: item.image?.large_images?.[0]?.image_url, imageUri: item.image?.large_images?.[0]?.image_uri}
      )) ?? [];

  return (
    <div className="p-4 mb-8 bg-white text-gray-800 rounded-lg shadow-md w-full my-2 border border-gray-200">
      <div className="mb-3">
        <h3 className="text-lg font-semibold text-purple-600">图像生成:{message.toolName}</h3>
      </div>
      {message.toolArgs  && (
          <div className="text-gray-400">
          <div style={{ marginBottom: 4 }}>工具调用参数</div>
          <ReactJsonView
            src={(() => {
              try {
                return JSON.parse(message.toolArgs ?? "");
              } catch (error) {
                return {};
              }
            })()}
          />
          </div>
      )}
      {message.toolResult  && (
          <div className="text-gray-400">
          <div style={{ marginBottom: 4 }}>工具调用结果</div>
          <ReactJsonView
            src={(() => {
              try {
                return JSON.parse(message.toolResult ?? "");
              } catch (error) {
                return {};
              }
            })()}
          />
          </div>
      )}
      {isInsufficient ? (
        <div className="mt-4 pt-3 border-t border-gray-200 flex justify-center items-center">
          <p className="text-sm text-red-500">积分不足。</p>
        </div>
      ):isFail ? (
            <div className="mt-4 pt-3 border-t border-gray-200 flex justify-center items-center">
              <p className="text-sm text-red-500">图片生成失败。</p>
            </div>
        ) :isGenerating ? (
        <div className="mt-4 pt-3 border-t border-gray-200 flex justify-center items-center">
          <p className="text-sm text-gray-600">正在生成图像，请稍候...</p>
        </div>
      ) : resultImages.length > 0 ? (
        <div className="mt-4 pt-3 border-t border-gray-200">
          <div className="grid grid-cols-2 gap-4">
            {resultImages.map(
              (imageData, index) =>
                imageData.imageUrl && (
                <ImageComponentWithReference 
                  imageUri={imageData.imageUri ?? ''}
                  imageUrl={imageData.imageUrl} 
                  index={index}
                  onReferenceHandler={onReferenceSingleImage}
                  getRefContent={() => {
                    const nuroFile = new NuroFile(
                      NuroFileType.image,
                      imageData.imageUrl,
                    )
                    nuroFile.uri = imageData.imageUri;
                    return [{file: nuroFile}]
                  }} />
                )
            )}
          </div>
        </div>
      ) : (
        <div className="mt-4 pt-3 border-t border-gray-200 flex justify-center items-center">
          <p className="text-sm text-gray-600">
            图片生成失败或未找到图片数据。
          </p>
        </div>
      )}
    </div>
  );
};




