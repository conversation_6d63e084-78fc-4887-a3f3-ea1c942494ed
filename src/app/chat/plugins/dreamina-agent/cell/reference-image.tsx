import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import withReference, { WithReferenceProps } from '../../../nuro-components/with-reference'; // Adjust path if necessary


// export interface ImageData {
//   imageUrl?: string,
//   imageUri?: string,
// }

interface ImageOwnProps {
  imageUri: string;
  imageUrl: string;
  index: number;
  // onReference is now handled by withReference, but we might need the original onReference from InsertText2ImageCell for other purposes or pass it to the HOC
  // For now, assuming onReferenceHandler in withReference is what we need.
}

const ImageComponent: React.FC<ImageOwnProps & WithReferenceProps> = ({ imageUri, imageUrl, index }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const [scale, setScale] = useState(1);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const handleDoubleClick = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setScale(1); // Reset scale on close
  };

  const handleWheel = (e: React.WheelEvent) => {
    e.preventDefault();
    e.stopPropagation();
    const newScale = scale - e.deltaY * 0.005;
    setScale(Math.min(Math.max(0.2, newScale), 5)); // Clamp scale
  };

  const modalContent = isClient && isModalOpen ? createPortal(
    <div
      className="fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm z-[9999] flex justify-center items-center"
      onClick={(e) => {
        e.stopPropagation();
        handleCloseModal();
      }}
      onWheel={(e) => e.stopPropagation()}
    >
      <div
        onClick={(e) => e.stopPropagation()} // Prevent closing modal when clicking on the image/modal content
        onWheel={handleWheel}
      >
        <img
          src={imageUrl}
          alt={`Generated Image ${index + 1}`}
          className="transition-transform duration-100 ease-out"
          style={{ transform: `scale(${scale})` }}
        />
        <button 
          onClick={handleCloseModal} 
          className="absolute top-2 right-2 bg-white rounded-full p-1 text-gray-800 hover:bg-gray-200"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </div>,
    document.body
  ) : null;

  return (
    <div className="relative">
      <img
        src={imageUrl}
        alt={`Generated Image ${index + 1}`}
        className="w-full h-auto rounded-lg shadow-md cursor-pointer"
        onDoubleClick={handleDoubleClick}
      />
      {/* The reference button will be added by the withReference HOC */}

      {modalContent}
    </div>
  );
};


export const ImageComponentWithReference = withReference<ImageOwnProps>(ImageComponent);