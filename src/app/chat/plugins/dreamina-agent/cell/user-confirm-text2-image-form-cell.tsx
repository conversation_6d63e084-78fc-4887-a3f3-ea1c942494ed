import React from "react";
import { ToolCallCellRendererProps } from "../../../plugin-system/plugin-types";
import { NuroUserMessage, NuroUtils } from "@byted/nurosdk-js";
import {getText2imageFormResult, Text2imageCallArg, text2imageFormText} from "@byted/dreamina_dml_js_web";

interface Text2ImageFormArgs {
  title?: string;
  prompt?: string;
  ratio?: string;
  instruction?: string;
  image_tags?: Array<string>;
}

// The toolResult for text2image_form contains the inputs again in a resource object
// For this cell, we are primarily interested in displaying the arguments (inputs) passed to the tool.

export const UserConfirmText2ImageFormCell: React.FC<
  ToolCallCellRendererProps
> = ({ message }) => {
  let toolArgs: Text2ImageFormArgs = {};
  try {
    // toolArgs are expected to be a JSON string of Text2ImageFormArgs
    if (message.toolArgs) {
      toolArgs = JSON.parse(message.toolArgs);
    }
  } catch (e) {
    console.error("Error parsing text2image_form tool args:", e);
  }

  const { title, prompt, ratio, instruction, image_tags } = toolArgs;

  return (
    <div className="p-4 mb-8 bg-white text-gray-800 rounded-lg shadow-md w-full my-2 border border-gray-200">
      <div className="mb-3">
        <h3 className="text-lg font-semibold text-purple-600">图像生成</h3>
        {title && <p className="text-sm text-gray-500 mt-1">{title}</p>}
      </div>

      {prompt && (
        <div className="mb-3">
          <p className="text-sm font-medium text-gray-600">推荐提示词</p>
          <p className="text-base bg-gray-100 text-gray-800 p-2 rounded mt-1 whitespace-pre-wrap">
            {prompt}
          </p>
        </div>
      )}

      {instruction && (
        <div className="mb-3">
          <p className="text-base text-gray-500 mt-1">{instruction}</p>
        </div>
      )}

      {/* Displaying a generic message as the tool's direct output is a confirmation */}
      <div className="mt-4 pt-3 border-t border-gray-200 flex justify-between items-center">
        <p className="text-sm text-gray-600">
          工具将根据以上参数请求生成图片。
        </p>
        {message.decodeToolCallResultToMCPFormat().content.length <= 0 && (
          <button
            onClick={() => {

              let textResource = new Text2imageCallArg()
              textResource.prompt = toolArgs.prompt??""
              textResource.ratio = toolArgs.ratio??""
              textResource.model = "high_aes_general_v30l:general_v3.0_18b"
              textResource.resolution = "1k"
              textResource.imageTags = toolArgs.image_tags??[]

              message.sendToolCallResultFromMCPFormat(getText2imageFormResult(textResource),
                  new NuroUserMessage(
                  NuroUtils.randomUUIDString(),
                  text2imageFormText
              ))
            }}
            className="px-4 py-2 bg-purple-500 hover:bg-purple-600 text-white text-sm font-medium rounded-md"
          >
            生成图片
          </button>
        )}
      </div>
    </div>
  );
};
