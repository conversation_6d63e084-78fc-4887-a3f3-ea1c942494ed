import React, {lazy} from "react";
import { ToolCallCellRendererProps } from "../../../plugin-system/plugin-types";
import { ToolUtils} from "@byted/dreamina_dml_js_web";
const ReactJsonView = lazy(() => import("@microlink/react-json-view"));

interface ContinueGenerateArgs {
  tip?: string;
}

export const PointReminderCell: React.FC<ToolCallCellRendererProps> = ({
  message,
}) => {

  let toolArgs: ContinueGenerateArgs = {};
  try {
    if (message.toolArgs) {
      toolArgs = JSON.parse(message.toolArgs);
    }
  } catch (e) {
    console.error("Error parsing continue_generate tool args:", e);
  }

  const handleContinue = () => {
    let result = ToolUtils.getContinueGenerateResult()
    message.sendToolCallResultFromMCPFormat(result)
    console.log("继续生成");

  };

  const handleDismiss = () => {
    let result = ToolUtils.getContinueGenerateResult()
    message.sendToolCallResultFromMCPFormat(result)
    console.log("不再提醒");
  };


  return (
    <div className="p-4 mb-8 bg-white text-gray-800 rounded-lg shadow-md w-full my-2 border border-gray-200">
      {message.toolArgs  && (
          <div className="text-gray-400">
            <div style={{ marginBottom: 4 }}>工具调用参数</div>
            <ReactJsonView
                src={(() => {
                  try {
                    return JSON.parse(message.toolArgs ?? "");
                  } catch (error) {
                    return {};
                  }
                })()}
            />
          </div>
      )}
      {message.toolResult  && (
          <div className="text-gray-400">
            <div style={{ marginBottom: 4 }}>工具调用结果</div>
            <ReactJsonView
                src={(() => {
                  try {
                    return JSON.parse(message.toolResult ?? "");
                  } catch (error) {
                    return {};
                  }
                })()}
            />
          </div>
      )}

        <p className="text-sm text-gray-600 mb-4">
          {toolArgs.tip || "可能会消耗大量积分,生成已暂停。"}
        </p>
        <div className="flex space-x-4">
          <button
            onClick={handleContinue}
            className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors"
          >
            继续生成
          </button>
          <button
            onClick={handleDismiss}
            className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
          >
            不再提醒
          </button>
        </div>
    </div>
  );
}; 