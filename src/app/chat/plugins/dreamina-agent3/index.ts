import {
    Nuro<PERSON><PERSON><PERSON><PERSON>Message,
    NuroTaskChecker,
  } from "@byted/nurosdk-js"; // Added import
  import {
    image2videoName,
    localText2imageName,
    pointsConsumptionReminderName, text2imageFormName,
    text2imageName,
    text2videoName,
  } from "@byted/dreamina_dml_js_web"; // Added import
  import {
    Plugin,
    PluginCapability,
    ToolCallCellRendererProps,
  } from "../../plugin-system/plugin-types";
  import {getToolServer} from "../dreamina-agent/dreamina-user-confirm-server";
  import { UserConfirmText2ImageFormCell } from "../dreamina-agent/cell/user-confirm-text2-image-form-cell"; // Added import
  import { LoacalGenerateText2ImageCell } from "../dreamina-agent/cell/local-generate-text2-image-cell";
  import { GenerateSingleVideoCell } from "../dreamina-agent/cell/generate-single-video-cell";
  import { PointReminderCell } from "../dreamina-agent/cell/point-reminder-cell";
  import {GenerateText2ImageCellWithReference} from "../dreamina-agent/cell/generate-text2-image-cell";
  
  const DreaminaAgent3Plugin: Plugin = {
    id: "dreamina_agent3",
    name: "Dreamina Agent3",
    description: "对应「即梦 3.0」线上的 Agent 功能，用于产品 Demo 演示和调试。",
    version: "3.0",
    capabilities: {
      [PluginCapability.CustomTaskChecker]: () => {
        return new NuroTaskChecker();
      },
      [PluginCapability.CustomMCPServer]: () => {
        return getToolServer()
      },
      [PluginCapability.CustomToolCallCell]: (
        message: NuroToolCallMessage
      ): React.FC<ToolCallCellRendererProps> | undefined => {
        if (message.toolName === text2imageFormName) {
          return UserConfirmText2ImageFormCell;
        }
        if (message.toolName === localText2imageName ) {
          return LoacalGenerateText2ImageCell;
        }
        if (message.toolName === text2imageName) {
          return GenerateText2ImageCellWithReference;
        }
        else if (message.toolName === text2videoName || message.toolName === image2videoName) {
          return GenerateSingleVideoCell;
        }
        else if (message.toolName === pointsConsumptionReminderName) {
          return PointReminderCell;
        }
        return undefined;
      },
    },
  };
  
  export default DreaminaAgent3Plugin;
  