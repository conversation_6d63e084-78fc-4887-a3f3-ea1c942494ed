import {PluginCenter} from "../plugin-system/plugin-center";
import DreaminaAgentPlugin from "../plugins/dreamina-agent";
import DreaminaAgent3Plugin from "../plugins/dreamina-agent3";

export interface HostAgentOptions {
  "x-hostagent-url"?: string;
  "x-hostagent-useproxy"?: "true" | "false";
  "x-hostagent-conversation-list-url"?: string;
  "x-hostagent-conversation-item-url"?: string;
  // Headers are dynamic, and other keys might be added from localStorage
  [key: string]: string | undefined;
}

export function initializeHostAgentOptions(): HostAgentOptions {
  const hostAgentOptions: Record<string, any> = {};

  const hostAgentUrl = localStorage.getItem("x-hostagent-url");
  const hostAgentUseProxy = localStorage.getItem("x-hostagent-useproxy");
  const hostAgentHeadersStr = localStorage.getItem("x-hostagent-headers");
  const conversationListUrl = localStorage.getItem(
    "x-hostagent-conversation-list-url"
  );
  const conversationItemUrl = localStorage.getItem(
    "x-hostagent-conversation-item-url"
  );
  const conversationResumeUrl = localStorage.getItem(
      "x-hostagent-conversation-resume-url"
  );
  const conversationStopUrl = localStorage.getItem(
      "x-hostagent-conversation-stop-url"
  );
  const scene = localStorage.getItem("agent-scene");

  const isFromTOS =
    new URL(window.location.href).searchParams.get("uid") === "tos";

  if (isFromTOS) {
    hostAgentOptions["x-hostagent-conversation-id"] = new URL(
      window.location.href
    ).searchParams.get("conversation_id");
  }

  if (hostAgentUrl) {
    hostAgentOptions["x-hostagent-url"] = hostAgentUrl;
  }

  if (hostAgentUseProxy === "true") {
    hostAgentOptions["x-hostagent-useproxy"] = hostAgentUseProxy;
  }

  let enableDreamina = PluginCenter.instance.isPluginEnabled(DreaminaAgentPlugin.id) || PluginCenter.instance.isPluginEnabled(DreaminaAgent3Plugin.id);
  if(enableDreamina){
    hostAgentOptions["x-use-ppe"] = 1;
    hostAgentOptions["pf"] = 7;
    hostAgentOptions["appid"] = 581595;
    hostAgentOptions["x-hostagent-cookie"] = 'env_id=recuP7ucrb2pUi; lvweb_env=team/dreamina-agent-3-0; _v2_spipe_web_id=7503533546057875492; s_v_web_id=verify_mal1zddc_2R1cRPLx_hEOY_49qa_8mIl_tSVFkdKz5cSD; fpk1=ee71fd602be416fdac582a8ff96f600f1be1830c1a4525befb5632383b5db8fc93e414167baf543d7c8ab9821f30b922; passport_csrf_token=3730900686d76b5eb24a09a85897f0f2; passport_csrf_token_default=3730900686d76b5eb24a09a85897f0f2; n_mh=9-mIeuD4wZnlYrrOvfzG3MuT6aQmCUtmr8FxV8Kl8xY; is_staff_user=false; user_spaces_idc={\\"7510133740774409228\\":\\"hl\\"}; sid_guard=a3b3ac485fe79c2df9a9721992380295%7C1750223894%7C5184000%7CSun%2C+17-Aug-2025+05%3A18%3A14+GMT; uid_tt=2f3669ac80b4f2636bc7187601827fa0; uid_tt_ss=2f3669ac80b4f2636bc7187601827fa0; sid_tt=a3b3ac485fe79c2df9a9721992380295; sessionid=a3b3ac485fe79c2df9a9721992380295; sessionid_ss=a3b3ac485fe79c2df9a9721992380295; sid_ucp_v1=1.0.0-KDU4OWY3YTgyN2VjN2MyZTE3MzNkMWE1NGQ3ZWYxYzI2N2YzNzQwMGQKHwi30-Cdx43oBhCWmMnCBhifrR8gDDDd_JyGBjgIQDUaAmxxIiBhM2IzYWM0ODVmZTc5YzJkZjlhOTcyMTk5MjM4MDI5NQ; ssid_ucp_v1=1.0.0-KDU4OWY3YTgyN2VjN2MyZTE3MzNkMWE1NGQ3ZWYxYzI2N2YzNzQwMGQKHwi30-Cdx43oBhCWmMnCBhifrR8gDDDd_JyGBjgIQDUaAmxxIiBhM2IzYWM0ODVmZTc5YzJkZjlhOTcyMTk5MjM4MDI5NQ; dm_auid=UM0jyfXoU4yG+oDj7WoO+mHnXiYQqaA6vy6qR1/uC0o=; uifid_temp=5c87dd253688485aca12fd78f4a0a5193dc230eb9daf64fe65e7beee15de9abfba5442c7ec86d4fa30e06aa448897ed1b45c4a7669af3137a6932959a66f4bd376c216770fd71027a6b713eabd2961d034005a5ace24d267456b521aa3d6d111112d5cd0c4bd7ebec544862d25011bf8; uifid=5c87dd253688485aca12fd78f4a0a5193dc230eb9daf64fe65e7beee15de9abfba5442c7ec86d4fa30e06aa448897ed197a50b9ac67f4286fd49ab29040cac166d2d55a2dc8a97aae49d46c1b148e4bbbce2ae2d68e0b3789480c016813411c10991d1d0882b9d6004e50e38f1584537d6631d4827b24071d1cf3bb50a1a353da4265482b5cba992fbbaea20597ad39e912b78be32ea0902282501fc7995bf1553c02bd3c2a7e1fab50b4987c32fd59ede9f068d4fcf07881ca649c546c85dd1; _tea_web_id=7503533493956773388; env=1; env_branch=team/dreamina-agent-3.0; env_host=cc-tools-env.gf.bytedance.net; env_reset_info=%E7%8E%AF%E5%A2%83%E5%B7%B2%E8%A2%AB%E9%94%80%E6%AF%81%EF%BC%8C%E8%AF%B7%E9%80%9A%E8%BF%87%E6%AD%A4%E9%93%BE%E6%8E%A5%E6%81%A2%E5%A4%8D%E7%8E%AF%E5%A2%83%EF%BC%9Ahttps://jimeng.jianying.com/envs/injectors/reset?ignore_env=1&redirect_url=https%253A%252F%252Fjimeng.jianying.com%252Fai-tool%20%0A%0A; ttwid=1|CuszwRz2H2cnQrSIUIZYEnA3Xa_RRpmefRQUffJGJuU|1751283169|bc2487cf40a0f33665ffb5a1fd0f06df8d30c138ea1b1841a3df1169e0c5048a; _uetsid=515ad4f0556c11f08d382364fb45061a; _uetvid=c69ab3902f2b11f0b327efd64212a1d2; odin_tt=241a2accf1da0f82776c273cd3df545707f3be4cf6678ab7d097d40bc231e9ad80315d93b84e6d862d32bfbd74dbd5736a5a2d215b02c0f03793d7856b4dcf8e';
    hostAgentOptions['x-tt-env'] = "ppe_agent3";
  }

  if (hostAgentHeadersStr) {
    try {
      const parsedHeaders = JSON.parse(hostAgentHeadersStr);
      if (Array.isArray(parsedHeaders)) {
        parsedHeaders.forEach((header: { key: string; value: string }) => {
          if (header.key && header.value) {
            // It's important that these headers are added directly to the options
            // object, not nested under a generic "headers" key, to match the
            // existing behavior in llmOptionsHelper.ts where they are spread into transportOptions.
            hostAgentOptions[header.key] = header.value;
          }
        });
      }
    } catch (e) {
      console.error(
        "Failed to parse x-hostagent-headers from localStorage:",
        e
      );
    }
  }

  if (conversationListUrl) {
    hostAgentOptions["x-hostagent-conversation-list-url"] = conversationListUrl;
  }

  if (conversationItemUrl) {
    hostAgentOptions["x-hostagent-conversation-item-url"] = conversationItemUrl;
  }

  if (conversationResumeUrl) {
    hostAgentOptions["x-hostagent-conversation-resume-url"] = conversationResumeUrl;
  }

  if (conversationStopUrl) {
    hostAgentOptions["x-hostagent-conversation-stop-url"] = conversationStopUrl;
  }

  if (scene) {
    hostAgentOptions["scene"] = scene;
  }

  return hostAgentOptions;
}

export async function dumpHostAgentOptionsToTOS(
  conversationId: string
): Promise<void> {
  try {
    const response = await fetch("/api/chat_share", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        conversationId: conversationId,
        ...initializeHostAgentOptions(),
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Failed to dump host agent options to TOS:", errorData);
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    console.log("Host agent options successfully dumped to TOS.");
  } catch (error) {
    console.error("Error dumping host agent options to TOS:", error);
    // Optionally re-throw the error or handle it as needed
    // throw error;
  }
}
