import { ReadonlyURLSearchParams } from "next/navigation";
import { initializeHostAgentOptions } from "./hostAgentOptionsHelper";

export interface PluginLLMOptions {
  model?: string | "deepseek-r1";
  endpoint?: string;
  apiKey?: string;
  systemprompt?: string;
  temperature?: string;
  mcpservers?: {
    name: string;
    url?: string;
    byteFaasConfig?: { psm: string; region: "BOE" | "CN" | "I18N" };
  }[];
}

export async function initializeTransportOptions(): Promise<
  Record<string, any>
> {
  const searchParams =
    typeof window === "object"
      ? new URL(window.location.href).searchParams
      : new Map();

  const transportOptions: Record<string, any> = {};

  // LLM Options
  const modelName = localStorage.getItem("x-llm-model");
  const endpoint = localStorage.getItem("x-llm-endpoint");
  const apiKey = localStorage.getItem("x-llm-apikey");
  const systemPrompt = localStorage.getItem("x-llm-systemprompt");
  const temperatureStr = localStorage.getItem("x-llm-temperature");

  if (modelName) transportOptions["x-llm-model"] = modelName;
  if (endpoint) transportOptions["x-llm-endpoint"] = endpoint;
  if (apiKey) transportOptions["x-llm-apikey"] = apiKey;
  if (systemPrompt)
    transportOptions["x-llm-systemprompt"] = encodeURIComponent(systemPrompt);
  if (temperatureStr) {
    const temperature = parseFloat(temperatureStr);
    if (!isNaN(temperature)) {
      transportOptions["x-llm-temperature"] = temperature;
    }
  }

  // MCP Servers
  const mcpServersStr = localStorage.getItem("x-llm-mcpservers");
  if (mcpServersStr) {
    try {
      const mcpServers = JSON.parse(mcpServersStr);
      if (Array.isArray(mcpServers)) {
        // Basic validation
        transportOptions["x-llm-mcpservers"] = JSON.stringify(mcpServers);
      }
    } catch (e) {
      console.error("Failed to parse mcpServers from localStorage:", e);
    }
  }

  if (searchParams.get("byteFaasPSM")) {
    const byteFaasServers: { id: string; psm: string; region: string }[] = [];
    searchParams
      .get("byteFaasPSM")
      ?.split(",")
      .forEach((it: string, idx: number) => {
        byteFaasServers.push({
          psm: it,
          id: searchParams.get("byteFaasID")?.split(",")[idx] ?? "_",
          region: (searchParams.get("byteFaasRegion")?.split(",")[idx] ??
            "BOE") as "BOE" | "CN" | "I18N",
        });
      });
    const originMCPServers =
      JSON.parse(transportOptions["x-llm-mcpservers"] ?? "[]") ?? [];
    byteFaasServers.forEach((element) => {
      originMCPServers.push({
        name: element.id,
        byteFaasConfig: element,
      });
    });
    transportOptions["x-llm-mcpservers"] = JSON.stringify(originMCPServers);
  }

  if (searchParams.get("sseUrl")) {
    const originMCPServers =
      JSON.parse(transportOptions["x-llm-mcpservers"] ?? "[]") ?? [];
    searchParams
      .get("sseUrl")
      ?.split(",")
      .forEach((it: string, index: number) => {
        originMCPServers.push({
          name: "tmp_sse_" + index,
          url: decodeURIComponent(it),
        });
      });
    transportOptions["x-llm-mcpservers"] = JSON.stringify(originMCPServers);
  }

  if (searchParams.get("byteFaasHeaders")) {
    transportOptions["x-llm-bytefaasheaders"] =
      searchParams.get("byteFaasHeaders");
  }

  // Host Agent Options
  const hostAgentOptions = initializeHostAgentOptions();
  for (const key in hostAgentOptions) {
    if (Object.prototype.hasOwnProperty.call(hostAgentOptions, key)) {
      transportOptions[key] = hostAgentOptions[key];
    }
  }

  return transportOptions;
}
