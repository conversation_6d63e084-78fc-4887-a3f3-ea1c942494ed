import {
  AIMessage,
  HumanMessage,
  mapStoredMessagesToChatMessages,
  ToolMessage,
  type BaseMessage, // mapStoredMessagesToChatMessages returns BaseMessage[]
} from "@langchain/core/messages";
import {
  NuroConversationManager,
  NuroUserMessage,
  NuroUserMessageStatus,
  NuroFile,
  NuroFileType,
  NuroReasoningMessage,
  NuroAssistantMessage,
  NuroAssistantMessageStatus,
  NuroToolCallMessage,
  NuroToolCallMessageStatus,
} from "@byted/nurosdk-js";
import { hostAgentEndpoint } from "../config"; // Import hostAgentEndpoint
import { initializeHostAgentOptions } from "./hostAgentOptionsHelper";

export async function loadAndProcessConversationHistory(
  conversationManager: NuroConversationManager,
  urlConversationId: string,
  urlUid: string | null
): Promise<void> {
  conversationManager.conversation.conversationId = urlConversationId;

  const customHostAgentConversationItemUrl = localStorage.getItem(
    "x-hostagent-conversation-item-url"
  );

  if (urlUid === "tos") {
    const res = await fetch("/api/chat_tos", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: `{"conversation_id": "${urlConversationId}","count": -1, "version":"3.0.0"}`,
    });
    const text = await res.json();
    const logId = res.headers.get("x-tt-logid");
    if (logId) {
            const logIdChunk = `\n[SYSTEM] X-TT-LogID: ${logId}\n\n`;
            const event = new CustomEvent("nuroEventStreamChunk", {
              detail: logIdChunk,
            });
            document.dispatchEvent(event);
          }
    const data = JSON.parse(text);
    conversationManager.decodeConversationFromJSONString(
      JSON.stringify(data.data)
    );
    conversationManager.conversation.messages.forEach((it) => {
      conversationManager.receivedMessage(it);
    });
  } else if (customHostAgentConversationItemUrl) {
    const hostAgentOptions = initializeHostAgentOptions();
    const res = await fetch("/api/chat_proxy", {
      method: "POST",
      headers: {
        ...hostAgentOptions,
        "x-url": customHostAgentConversationItemUrl,
        "Content-Type": "application/json",
      },
      body: `{"version": "3.0.0","conversation_id": "${urlConversationId}","count": -1}`,
    });
    const text = await res.json();
    const data = JSON.parse(text);
    conversationManager.decodeConversationFromJSONString(
      JSON.stringify(data.data)
    );
    conversationManager.conversation.messages.forEach((it) => {
      conversationManager.receivedMessage(it);
    });
  } else {
    const fetchUrl = `${hostAgentEndpoint}/conversation/${urlConversationId}/${urlUid}`;

    const response = await fetch(fetchUrl, {
      method: "POST",
      credentials: "include",
    });

    if (!response.ok) {
      console.error(
        `Failed to fetch conversation history: ${response.status} ${response.statusText}`
      );
      try {
        const errorData = await response.text();
        console.error("Error data:", errorData);
      } catch (e) {
        console.error("Failed to get error text from response", e);
      }
      return;
    }

    let responseData;
    try {
      responseData = await response.json();
    } catch (e) {
      console.error("Failed to parse response JSON", e);
      return;
    }

    const messages: BaseMessage[] =
      mapStoredMessagesToChatMessages(responseData);

    console.log("messages", messages);

    messages.forEach((message, msgIdx) => {
      if (message instanceof HumanMessage) {
        if (typeof message.content === "string" && message.content.length > 0) {
          const userMessage = new NuroUserMessage(
            message.id + "_text",
            message.content,
            []
          );
          userMessage.messageStatus =
            NuroUserMessageStatus.finished_successfully;
          conversationManager.receivedMessage(userMessage);
        } else if (Array.isArray(message.content)) {
          message.content.forEach((item: any, index: number) => {
            if (item.type === "image_url" && item.image_url?.url) {
              const userMessage = new NuroUserMessage(
                message.id + "_image_" + index + "_" + msgIdx,
                undefined,
                [new NuroFile(NuroFileType.image, item.image_url.url)]
              );
              userMessage.messageStatus =
                NuroUserMessageStatus.finished_successfully;
              conversationManager.receivedMessage(userMessage);
            } else if (
              item.type === "text" &&
              item.text.indexOf("用户上传了一个视频，视频 URL = ") === 0
            ) {
              const userMessage = new NuroUserMessage(
                message.id + "_video_" + index + "_" + msgIdx,
                undefined,
                [
                  new NuroFile(
                    NuroFileType.video,
                    item.text.replace("用户上传了一个视频，视频 URL = ", "")
                  ),
                ]
              );
              userMessage.messageStatus =
                NuroUserMessageStatus.finished_successfully;
              conversationManager.receivedMessage(userMessage);
            }
          });
        }
      } else if (message instanceof AIMessage) {
        if (message.additional_kwargs?.reasoning_content) {
          const reasoningMessage = new NuroReasoningMessage(
            message.id + "_reasoning",
            message.additional_kwargs.reasoning_content as string
          );
          reasoningMessage._rawId = message.id ?? "";
          conversationManager.receivedMessage(reasoningMessage);
        }

        if (
          typeof message.content === "string" &&
          message.content.trim().length > 0
        ) {
          const assistantMessage = new NuroAssistantMessage(
            message.id + "_assistant",
            undefined,
            message.content,
            []
          );
          assistantMessage._rawId = message.id ?? "";
          assistantMessage.messageStatus =
            NuroAssistantMessageStatus.finished_successfully;
          conversationManager.receivedMessage(assistantMessage);
        }

        if (message.tool_calls && message.tool_calls.length > 0) {
          message.tool_calls.forEach((toolCall: any) => {
            if (toolCall.id) {
              const toolCallMessage = new NuroToolCallMessage(
                message.id + "_tool_call_" + toolCall.id,
                toolCall.id,
                "server_function",
                toolCall.name,
                JSON.stringify(toolCall.args),
                undefined
              );
              toolCallMessage._rawId = message.id ?? "";
              toolCallMessage.messageStatus =
                NuroToolCallMessageStatus.invoking;
              conversationManager.receivedMessage(toolCallMessage);
            }
          });
        }
      } else if (message instanceof ToolMessage) {
        conversationManager.conversation.messages.forEach((it) => {
          if (
            it instanceof NuroToolCallMessage &&
            it.toolCallId === message.tool_call_id
          ) {
            if (typeof message.content === "string") {
              it.toolResult = message.content;
              try {
                const parsedResult = JSON.parse(it.toolResult);
                if (
                  parsedResult &&
                  typeof parsedResult.content !== "undefined" &&
                  (typeof parsedResult.content === "string" ||
                    Array.isArray(parsedResult.content)) &&
                  parsedResult.content.length > 0
                ) {
                  it.messageStatus =
                    NuroToolCallMessageStatus.finished_successfully;
                }
              } catch (error) {
                console.error("Error processing tool message result:", error);
              }
              conversationManager.receivedMessage(it);
            }
          }
        });
      }
    });
    console.log(
      "Fetched and processed conversation data from URL parameters:",
      responseData
    );
  }
}
