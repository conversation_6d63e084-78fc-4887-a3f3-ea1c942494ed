import { NuroMessage, NuroTask, RefContent } from "@byted/nurosdk-js";
import { NuroCellBuilder } from "./builder";
import { MiddlewareCard } from "./middleware-card";

interface TaskCellBuilderProps {
  task: NuroTask;
  onRetry?: (message: NuroMessage) => void; // 添加 onRetry 属性
  onReference?: (message: RefContent[]) => void; // 添加reference
}

export const NuroTaskCellBuilder: React.FC<TaskCellBuilderProps> = ({
  task,
  onRetry,
  onReference,
}) => {
  return (
    <>
      {task.promptMessages.map((message) => (
        <NuroCellBuilder key={message.id} message={message} onRetry={onRetry} />
      ))}
      <MiddlewareCard messages={task.middlewareMessages} onRetry={onRetry} />
      {task.artifactMessages.map((message) => (
        <NuroCellBuilder key={message.id} message={message} onRetry={onRetry} onReference={onReference}/>
      ))}
    </>
  );
};
