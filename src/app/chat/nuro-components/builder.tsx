import {
  NuroAssistantMessage,
  NuroMessage,
  NuroReasoningMessage,
  NuroToolCallMessage,
  NuroCanvasMessage,
  NuroUserMessage,
  RefContent,
} from "@byted/nurosdk-js";
import React, { useState } from "react"; // 导入 useState
import { UserCell } from "./user-text";
import { AssistantWithRef } from "./assistant-text";
import { ToolCallCellWithRef } from "./tool-call";
import { ReasoningCellWithReference } from "./reasoning-cell";
import { CanvasToolWithReference } from "./canvas-tool";
import { PluginCenter } from "../plugin-system/plugin-center"; 
import { nuroMessageToRefContent } from "../common/ref_utils";
import { DebugButton } from "../components/debug-button";
import { useAtomValue } from 'jotai';
import { getLogIdAtom } from "../store/messageLogStore";

interface CellBuilderProps {
  message: NuroMessage;
  onRetry?: (message: NuroMessage) => void; 
  onReference?: (refContentArray: RefContent[]) => void; // 新增引用回调
  hideTextContent?: boolean;
  logId?: string;
  onArgosClick?: (logId: string) => void;
  onFornaxClick?: (logId: string) => void;
}

export const NuroCellBuilder: React.FC<CellBuilderProps> = ({
  message,
  onRetry,
  hideTextContent,
  onReference, // 接收引用回调
  logId,
  onArgosClick,
  onFornaxClick,
}) => {
  // 使用Jotai的响应式atom获取当前消息对应的logId
  const getLogId = useAtomValue(getLogIdAtom);
  const actualLogId = getLogId(message.id) || logId || '';

  const cellContent = () => {
    if (message instanceof NuroAssistantMessage) {
      if (hideTextContent) return null;
      const assistantContent = <AssistantWithRef key={message.id} message={message} onReferenceHandler={onReference} getRefContent = {() => nuroMessageToRefContent(message)} hideTextContent={hideTextContent}/>;
      
      // 如果是对话的结尾，添加调试按钮
      if (message.endTurn === true || message.messageStatus === 'finished_successfully') {
        return (
          <div>
            <div>{assistantContent}</div>
            <div className="ml-4">
              <DebugButton 
                logId={actualLogId}
                onArgosClick={onArgosClick}
                onFornaxClick={onFornaxClick}
              />
            </div>
          </div>
        );
      }
      
      return assistantContent;
    } else if (message instanceof NuroToolCallMessage) {
      const CustomToolCallCell =
        PluginCenter.instance.getToolCallCellRenderer(message);
      if (CustomToolCallCell) {
        return <CustomToolCallCell key={message.id} message={message} onReferenceSingleImage={onReference} onReferenceHandler={onReference} getRefContent = {() => nuroMessageToRefContent(message)}/>;
      }
      return <ToolCallCellWithRef key={message.id} message={message} onReferenceHandler={onReference} getRefContent = {() => nuroMessageToRefContent(message)}/>;
    } else if (message instanceof NuroReasoningMessage) {
      if (hideTextContent) return null;
      return <ReasoningCellWithReference key={message.id} message={message} onReferenceHandler={onReference} getRefContent = {() => nuroMessageToRefContent(message)} hideTextContent={hideTextContent}/>;
    } else if (message instanceof NuroCanvasMessage) {
      return <CanvasToolWithReference key={message.id} message={message} onReferenceHandler={onReference} onReferenceSingleImage={onReference} getRefContent = {() => nuroMessageToRefContent(message)}/>
    }
    
    return <></>;
  };

  if (message instanceof NuroUserMessage) {
    return (
      <UserCell
        key={message.id}
        message={message}
        onRetry={onRetry ? () => onRetry(message) : undefined}
      />
    );
  } else {
    // 对于非用户消息，添加 hover 效果和引用按钮
    return (
        cellContent()
    );
  }
};






