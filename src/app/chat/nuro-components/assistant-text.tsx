import { NuroAssistantMessage, NuroAssistantMessageStatus, NuroFile, NuroFileType } from "@byted/nurosdk-js";
import React from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import withReference from "./with-reference";

interface AssistantCellProps {
  message: NuroAssistantMessage;
}

const AssistantCell: React.FC<AssistantCellProps> = ({ message }) => {
  return (
    <div className="p-6 rounded-2xl bg-gray-50 border border-gray-200 transition-all duration-200 hover:bg-gray-100">
      <div className="flex items-center gap-2 mb-3">
        <div className="w-6 h-6 rounded-full bg-blue-500 flex items-center justify-center text-white text-xs font-medium">
          AI
        </div>
        <div className="font-semibold text-gray-800">Assistant{message.messageStatus === NuroAssistantMessageStatus.streaming ? "（Streaming...）" : ""}</div>
      </div>
      <div className="text-gray-700 leading-relaxed my-user-text">
        <ReactMarkdown key={message.id} remarkPlugins={[remarkGfm]}>
          {message.text?.trim()}
        </ReactMarkdown>
        {message.files && message.files.length > 0 && (
          <div className="mt-3">
            <h4 className="text-md font-medium mb-2">Files:</h4>
            {message.files.map((file: NuroFile, index: number) => (
              <div key={index} className="mb-1">
                {file.type === NuroFileType.image && file.url ? (
                  <img src={file.url}  className="max-w-full h-auto rounded-lg" />
                ) : file.type === NuroFileType.video && file.url ? (
                  <video src={file.url} controls className="max-w-full h-auto rounded-lg" />
                ) : null}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export const AssistantWithRef = withReference(AssistantCell);
