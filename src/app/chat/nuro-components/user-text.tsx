import { NuroUserMessage, NuroUserMessageStatus } from "@byted/nurosdk-js";
import React from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";

interface UserCellProps {
  message: NuroUserMessage;
  onRetry?: () => void; // 添加 onRetry 属性
}

export const UserCell: React.FC<UserCellProps> = ({ message, onRetry }) => {
  return (
    <div className="p-6 rounded-2xl bg-gray-50 border border-gray-200 transition-all duration-200 hover:bg-gray-100">
      <div className="flex items-center gap-2 mb-3">
        <div className="w-6 h-6 rounded-full bg-gray-700 flex items-center justify-center text-white text-xs font-medium">
          You
        </div>
        <div className="font-semibold text-gray-800">User</div>
      </div>
      <div className="text-gray-700 leading-relaxed my-user-text">
        {message.files?.map((file, index) => {
          if (file.type === "image") {
            return (
              <img
                key={"file_" + index}
                src={file.url}
                style={{ maxWidth: "200px", marginBottom: "10px" }}
              />
            );
          } else if (file.type === "video") {
            return (
              <video
                key={"file_" + index}
                src={file.url}
                style={{ maxWidth: "200px", marginBottom: "10px" }}
                controls
              />
            );
          }
          return <></>;
        })}
        {message.referenceInfo && message.referenceInfo.length > 0 && (
          <div className="mb-2 space-y-1">
            {/* First pass: render 'file' types */}
            {message.referenceInfo.map((refItem, index) => {
              if (refItem.file !== undefined) {
                return (
                  <div 
                    key={`ref_file_${index}`} 
                    className="p-2 bg-gray-200 rounded-lg text-sm text-gray-600 flex items-center overflow-hidden whitespace-nowrap"
                    title={refItem.text} // Show full text on hover
                  >
                    {refItem.file.url && (
                      <img 
                        src={refItem.file.url} 
                        alt="reference image" 
                        className="w-8 h-8 object-cover rounded mr-2" // Adjust size and style as needed
                      />
                    )}
                    <span className="text-ellipsis overflow-hidden">
                      {refItem.text}
                    </span>
                  </div>
                );
              }
              return null;
            })}
            {/* Second pass: render 'text' types */}
            {message.referenceInfo.map((refItem, index) => {
              if (refItem.text !== undefined) {
                return (
                  <div 
                    key={`ref_text_${index}`} 
                    className="p-2 bg-gray-200 rounded-lg text-sm text-gray-600 overflow-hidden text-ellipsis whitespace-nowrap"
                    title={refItem.text} // Show full text on hover
                  >
                    <span className="text-ellipsis overflow-hidden">
                      {refItem.text}
                    </span>
                  </div>
                );
              }
              return null;
            })}
          </div>
        )}
        <ReactMarkdown key={message.id} remarkPlugins={[remarkGfm]}>
          {message.text?.trim()}
        </ReactMarkdown>
        {message.messageStatus === NuroUserMessageStatus.failed && (
          <div className="mt-2 text-red-500 text-sm flex items-center gap-2">
            <span>发送失败</span>
            {onRetry && (
              <button
                onClick={onRetry}
                className="text-blue-500 hover:underline focus:outline-none"
                style={{ cursor: "pointer" }}
              >
                重试
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
};
