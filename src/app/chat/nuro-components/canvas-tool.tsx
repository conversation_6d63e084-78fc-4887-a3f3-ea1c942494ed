import React from 'react';
import { NuroCanvasMessage, NuroCanvasNode, NuroToolCallMessage, RefContent } from '@byted/nurosdk-js'; // 确保 NuroToolCallMessage 也被导入，以便类型检查
import { InsertText2ImageCell } from '../plugins/dreamina-agent/cell/insert-text2-image-cell';
import { InsertSingleVideoCell } from '../plugins/dreamina-agent/cell/insert-single-video-cell';
import { image2imageV3Name, image2videoV3Name, start2EndVideoV3Name, text2imageV3Name, text2videoV3Name } from '@byted/dreamina_dml_js_web';
import { withReference } from './with-reference'; 

interface CanvasCellProps {
  message: NuroCanvasMessage;
  onReferenceSingleImage?: (message: RefContent[]) => void;
}

const CanvasCell: React.FC<CanvasCellProps> = ({ message, onReferenceSingleImage }) => {
  const canvasName = 'Canvas';
  const nodes: NuroCanvasNode[] = 
    message.nodes && Array.isArray(message.nodes) 
      ? message.nodes 
      : [];
    
  return (
    <div className="p-4 my-2 rounded-lg bg-gray-50 border border-gray-200 shadow-sm">
      <div className="flex items-center gap-2 mb-3">
        <div className="w-6 h-6 rounded-full bg-blue-500 flex items-center justify-center text-white text-xs font-semibold">
          C
        </div>
        <div className="font-medium text-md text-gray-700">
          {canvasName} (ID: {message.id})
        </div>
      </div>

      
      {nodes.length > 0 ? (
        <div className="flex overflow-x-auto space-x-3 pb-2"> 
          {nodes.map((node) => {
            // 根据 toolName 渲染不同的组件
              if (node.toolName === text2imageV3Name || 
                node.toolName === image2imageV3Name) {
              return <InsertText2ImageCell key={node.id} message={node as NuroToolCallMessage} onReferenceSingleImage={onReferenceSingleImage} />;
            } else if (node.toolName === text2videoV3Name|| node.toolName === image2videoV3Name || node.toolName === start2EndVideoV3Name
            ) {
              return <InsertSingleVideoCell key={node.id} message={node as NuroToolCallMessage} />;
            }
            //  如果toolName不匹配任何已知类型，可以选择渲染一个默认组件或null
            //  为了避免错误，这里返回null，您可以根据需要调整
            console.warn(`Unknown toolName: ${node.toolName} for node id: ${node.id}`);
            return null; 
            })
          }
        </div>
      ) : (
        <p className="text-sm text-gray-500 italic">Loading...</p>
      )}
      
    </div>
  );
};

export const CanvasToolWithReference = withReference(CanvasCell);
