import {
  NuroReasoningMessage,
  NuroReasoningMessageStatus,
} from "@byted/nurosdk-js";
import React from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { withReference } from "./with-reference";

interface ReasoningCellProps {
  message: NuroReasoningMessage;
}

const ReasoningCell: React.FC<ReasoningCellProps> = ({ message }) => {
  // Basic styling, can be customized further
  return (
    <div className="p-4 my-2 rounded-lg bg-yellow-50 border border-yellow-200 hover:opacity-100 transition-opacity duration-200">
      <div className="flex items-center gap-2 mb-2">
        {/* Optional: Add an icon or indicator for reasoning */}
        {/* <div className="w-5 h-5 rounded-full bg-yellow-400 flex items-center justify-center text-white text-xs font-medium">R</div> */}
        <div className="font-medium text-sm text-yellow-700">
          Reasoning
          {message.messageStatus === NuroReasoningMessageStatus.streaming
            ? "（Reasoning...）"
            : ""}
        </div>
      </div>
      <div className="text-sm text-gray-600 leading-relaxed whitespace-pre-wrap my-reasoning-text">
        <ReactMarkdown key={message.id} remarkPlugins={[remarkGfm]}>
          {message.text?.trim().replace(/\n/g, "\n\n")}
        </ReactMarkdown>
      </div>
    </div>
  );
};

export const ReasoningCellWithReference = withReference(ReasoningCell)
