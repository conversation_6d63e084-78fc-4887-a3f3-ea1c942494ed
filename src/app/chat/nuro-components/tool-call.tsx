import React from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import {
  NuroToolCallMessage,
  NuroToolCallMessageStatus,
  RefContent,
} from "@byted/nurosdk-js";
import { lazy } from "react";
const ReactJsonView = lazy(() => import("@microlink/react-json-view"));
import { withReference } from './with-reference';

interface ToolCallCellProps {
  message: NuroToolCallMessage;
  onReference?: (refContentArray: RefContent[]) => void;
}

export const ToolCallCell: React.FC<ToolCallCellProps> = ({ message }) => {
  return (
    <div
      className="p-6 rounded-2xl bg-white shadow-lg transition-all duration-200 hover:shadow-xl border border-gray-100"
      style={{ marginBottom: 20 }}
    >
      <div className="flex items-center gap-2 mb-3">
        <div className="w-6 h-6 rounded-full bg-orange-500 flex items-center justify-center text-white text-xs font-medium">
          T
        </div>
        <div className="font-semibold text-gray-800">
          工具调用 - {message.toolName}
        </div>
      </div>
      <div className="text-gray-400">
        <div style={{ marginBottom: 4 }}>工具调用参数</div>
        <ReactJsonView
          src={(() => {
            try {
              return JSON.parse(message.toolArgs ?? "");
            } catch (error) {
              return {};
            }
          })()}
        />
      </div>
      <div className="text-gray-700 leading-relaxed prose prose-sm max-w-none" style={{overflowX: "hidden"}}>
        {message.messageStatus ===
        NuroToolCallMessageStatus.finished_successfully ? (
          <div
            style={{ marginBottom: 4, marginTop: 10 }}
            className="text-gray-400"
          >
            工具调用结果
          </div>
        ) : (
          <div
            style={{ marginBottom: 4, marginTop: 10 }}
            className="text-gray-400"
          >
            正在调用...
          </div>
        )}
        {(() => {
          if (
            message.messageStatus ===
              NuroToolCallMessageStatus.finished_successfully ||
            message.messageStatus === NuroToolCallMessageStatus.invoking
          ) {
            try {
              const data = JSON.parse(message.toolResult ?? "{}");
              if (data.content instanceof Array) {
                return data.content.map((item: any, idx: number) => {
                  if (item.type === "text" || typeof item.text === "string") {
                    return (
                      <ReactMarkdown
                        key={"key_" + idx}
                        remarkPlugins={[remarkGfm]}
                      >
                        {item.text.trim().replace(/\n/g, "\n\n")}
                      </ReactMarkdown>
                    );
                  } else if (item.type === "resource" && item.resource) {
                    if (item.resource.mimeType?.startsWith("image")) {
                      return (
                        <a
                          key={"key_" + idx}
                          href={item.resource.uri}
                          target="_blank"
                        >
                          <img
                            src={item.resource.uri}
                            style={{ maxWidth: "200px", marginBottom: "10px" }}
                          />
                        </a>
                      );
                    } else if (item.resource.mimeType?.startsWith("video")) {
                      return (
                        <a href={item.resource.uri} target="_blank">
                          <video
                            key={"key_" + idx}
                            src={item.resource.uri}
                            style={{ maxWidth: "200px", marginBottom: "10px" }}
                          />
                        </a>
                      );
                    } else if (item.resource.mimeType === "text/html") {
                      return (
                        <ReactJsonView
                          src={(() => {
                            try {
                              return JSON.parse(item.resource.text);
                            } catch (error) {
                              return { content: item.resource.text };
                            }
                          })()}
                        />
                      );
                    } else {
                      return <div key={"key_" + idx}></div>;
                    }
                  } else {
                    return <div key={"key_" + idx}></div>;
                  }
                });
              } else {
                return (
                  <ReactJsonView
                    src={(() => {
                      try {
                        return data;
                      } catch (error) {
                        return {};
                      }
                    })()}
                  />
                );
              }
            } catch (error) {
              return <div>{message.toolResult}</div>
            }
          }
          return <></>;
        })()}
      </div>
    </div>
  );
};

export const ToolCallCellWithRef = withReference(ToolCallCell);
