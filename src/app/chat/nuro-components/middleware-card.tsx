import React from "react";
import {
  NuroAssistantMessage,
  NuroAssistantMessageStatus,
  NuroMessage,
  NuroToolCallMessage,
  NuroToolCallMessageStatus,
} from "@byted/nurosdk-js";
import { NuroCellBuilder } from "./builder";

interface MiddlewareCardProps {
  messages: NuroMessage[];
  onRetry?: (message: NuroMessage) => void;
  onReference?: (message: NuroMessage) => void;
}

export const MiddlewareCard: React.FC<MiddlewareCardProps> = ({
  messages,
  onRetry,
  onReference,
}) => {
  if (!messages || messages.length === 0) {
    return null;
  }

  return (
    <div className="border border-gray-300 rounded-lg p-4 my-4 bg-gray-50">
      <h3 className="mt-0 mb-3 text-base font-bold text-gray-800">任务过程</h3>
      <div className="space-y-4">
        {messages.map((message, index) => {
          let isProcessing: boolean = false;
          if (message instanceof NuroToolCallMessage) {
            isProcessing =
              message instanceof NuroToolCallMessage &&
              (message.messageStatus === NuroToolCallMessageStatus.streaming ||
                message.messageStatus === NuroToolCallMessageStatus.invoking);
          }
          if (message instanceof NuroAssistantMessage) {
            isProcessing =
              message instanceof NuroAssistantMessage &&
              message.messageStatus === NuroAssistantMessageStatus.streaming;
          }
          return (
            <div key={message.id} className="flex items-start">
              {/* Step Indicator */}
              <div className="flex-shrink-0 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold mr-4">
                {isProcessing ? (
                  <svg
                    className="animate-spin h-5 w-5 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                ) : (
                  index + 1
                )}
              </div>
              {/* Message Content */}
              <div className="flex-grow">
                {message instanceof NuroToolCallMessage && (
                  <div className="bg-blue-100 text-gray-700 p-2 rounded-md text-sm mb-2">
                    调用工具 - {message.toolName}
                  </div>
                )}
                <NuroCellBuilder message={message} onRetry={onRetry} />
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};
