import React, { useState, ComponentType } from 'react';
import { RefContent } from '@byted/nurosdk-js';



export type OnReferenceFn = (refContent: RefContent[]) => void;
export type getRefContentFn = () => RefContent[];

export interface WithReferenceProps {
  onReferenceHandler?: OnReferenceFn | undefined;
  getRefContent?:getRefContentFn | undefined;
  hideTextContent?: boolean | undefined;
}

export function withReference<P extends object>(
  WrappedComponent: ComponentType<P>
) {
  const ComponentWithReference = (props: P & WithReferenceProps) => {
    const [showReferenceButton, setShowReferenceButton] = useState(false);
    const { onReferenceHandler, getRefContent, ...rest } = props;

    const handleReferenceClick = (e: React.MouseEvent) => {
      e.stopPropagation(); // Prevent event bubbling
      e.preventDefault(); // Prevent default action
      // 尝试从 WrappedComponent 获取引用内容
      // WrappedComponent 需要通过某种方式提供 getRefContent 的实现
      // 这里我们假设 WrappedComponent 会接收 getRefContent 作为 prop
      // 并在内部实现它，或者 HOC 的调用者会提供一个 getRefContent 实现

      if (typeof (props as any).getRefContent === 'function') {
        const refContentArray = getRefContent?.();
        console.log("refContentArray is " , JSON.stringify(refContentArray));
        if (refContentArray && refContentArray.length > 0) {
          onReferenceHandler?.(refContentArray);
        }
      } else {
        console.warn(
          'getRefContent function not provided or not implemented in WrappedComponent. Cannot get reference content.'
        );
      }
    };

    return (
      <div
        style={{ position: 'relative', display: 'inline-block' }} // 确保 div 包裹内容且相对定位生效
        onMouseEnter={() => setShowReferenceButton(true)}
        onMouseLeave={() => setShowReferenceButton(false)}
      >
        <WrappedComponent {...(rest as P)} />
        {showReferenceButton && (
          <button
            onClick={handleReferenceClick}
            style={{
              position: 'absolute',
              top: '5px', // Changed from bottom to top
              right: '5px',
              zIndex: 1000, // 确保按钮在最上层
              background: 'rgba(255, 255, 255, 0.8)', // Optional: for better visibility
              border: '1px solid #ccc', // Optional: for better visibility
              borderRadius: '50%', // Make it circular
              padding: '4px', // Adjust padding for icon size
              cursor: 'pointer',
              display: 'flex', // For centering icon
              alignItems: 'center',
              justifyContent: 'center',
              width: '24px', // Explicit width
              height: '24px', // Explicit height
            }}
            title="Reference this item" // Tooltip for accessibility
          >
            {/* Replace with your desired SVG icon */}
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" className="bi bi-paperclip" viewBox="0 0 16 16">
              <path d="M4.5 3a2.5 2.5 0 0 1 5 0v9a1.5 1.5 0 0 1-3 0V5a.5.5 0 0 1 1 0v7a.5.5 0 0 0 1 0V3a1.5 1.5 0 1 0-3 0v9a2.5 2.5 0 0 0 5 0V5a.5.5 0 0 1 1 0v7a3.5 3.5 0 1 1-7 0V3z"/>
            </svg>
          </button>
        )}
      </div>
    );
  };

  // 返回一个新的组件，它接收一个 onReferenceHandler prop
  // 并将 onReference (由 onReferenceHandler 实现) 和 getRefContent (期望由外部提供) 注入到 WrappedComponent
  return (hocProps: Omit<P, keyof WithReferenceProps> & WithReferenceProps) => {
    const { onReferenceHandler, getRefContent, ...originalProps } = hocProps;
    return <ComponentWithReference {...originalProps as P} onReferenceHandler={onReferenceHandler} getRefContent={getRefContent} />;
  };
}

export default withReference;