import React, { useState, useEffect } from "react";
import { PluginMetadata } from "../plugin-system/plugin-types";
import { PluginCenter } from "../plugin-system/plugin-center";
import DreaminaAgentPlugin from "../plugins/dreamina-agent";
import DreaminaAgent3Plugin from "../plugins/dreamina-agent3";

interface SettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface McServerEntry {
  id: string;
  name: string;
  url: string;
}

interface HostAgentHeaderEntry {
  id: string;
  key: string;
  value: string;
}

export const defaultHostAgent = [
    { id: Date.now().toString() + "_header_1", key: "x-use-ppe", value: "1" },
    { id: Date.now().toString() + "_header_2", key: "x-tt-env", value: "ppe_agent3" },
    { id: Date.now().toString() + "_header_3", key: "x-hostagent-cookie", value: "env_id=recuP7ucrb2pUi; lvweb_env=team/dreamina-agent-3-0; _v2_spipe_web_id=7503533546057875492; s_v_web_id=verify_mal1zddc_2R1cRPLx_hEOY_49qa_8mIl_tSVFkdKz5cSD; fpk1=ee71fd602be416fdac582a8ff96f600f1be1830c1a4525befb5632383b5db8fc93e414167baf543d7c8ab9821f30b922; passport_csrf_token=3730900686d76b5eb24a09a85897f0f2; passport_csrf_token_default=3730900686d76b5eb24a09a85897f0f2; n_mh=9-mIeuD4wZnlYrrOvfzG3MuT6aQmCUtmr8FxV8Kl8xY; is_staff_user=false; user_spaces_idc={\"7510133740774409228\":\"hl\"}; sid_guard=a3b3ac485fe79c2df9a9721992380295%7C1750223894%7C5184000%7CSun%2C+17-Aug-2025+05%3A18%3A14+GMT; uid_tt=2f3669ac80b4f2636bc7187601827fa0; uid_tt_ss=2f3669ac80b4f2636bc7187601827fa0; sid_tt=a3b3ac485fe79c2df9a9721992380295; sessionid=a3b3ac485fe79c2df9a9721992380295; sessionid_ss=a3b3ac485fe79c2df9a9721992380295; sid_ucp_v1=1.0.0-KDU4OWY3YTgyN2VjN2MyZTE3MzNkMWE1NGQ3ZWYxYzI2N2YzNzQwMGQKHwi30-Cdx43oBhCWmMnCBhifrR8gDDDd_JyGBjgIQDUaAmxxIiBhM2IzYWM0ODVmZTc5YzJkZjlhOTcyMTk5MjM4MDI5NQ; ssid_ucp_v1=1.0.0-KDU4OWY3YTgyN2VjN2MyZTE3MzNkMWE1NGQ3ZWYxYzI2N2YzNzQwMGQKHwi30-Cdx43oBhCWmMnCBhifrR8gDDDd_JyGBjgIQDUaAmxxIiBhM2IzYWM0ODVmZTc5YzJkZjlhOTcyMTk5MjM4MDI5NQ; dm_auid=UM0jyfXoU4yG+oDj7WoO+mHnXiYQqaA6vy6qR1/uC0o=; uifid_temp=5c87dd253688485aca12fd78f4a0a5193dc230eb9daf64fe65e7beee15de9abfba5442c7ec86d4fa30e06aa448897ed1b45c4a7669af3137a6932959a66f4bd376c216770fd71027a6b713eabd2961d034005a5ace24d267456b521aa3d6d111112d5cd0c4bd7ebec544862d25011bf8; uifid=5c87dd253688485aca12fd78f4a0a5193dc230eb9daf64fe65e7beee15de9abfba5442c7ec86d4fa30e06aa448897ed197a50b9ac67f4286fd49ab29040cac166d2d55a2dc8a97aae49d46c1b148e4bbbce2ae2d68e0b3789480c016813411c10991d1d0882b9d6004e50e38f1584537d6631d4827b24071d1cf3bb50a1a353da4265482b5cba992fbbaea20597ad39e912b78be32ea0902282501fc7995bf1553c02bd3c2a7e1fab50b4987c32fd59ede9f068d4fcf07881ca649c546c85dd1; _tea_web_id=7503533493956773388; env=1; env_branch=team/dreamina-agent-3.0; env_host=cc-tools-env.gf.bytedance.net; env_reset_info=%E7%8E%AF%E5%A2%83%E5%B7%B2%E8%A2%AB%E9%94%80%E6%AF%81%EF%BC%8C%E8%AF%B7%E9%80%9A%E8%BF%87%E6%AD%A4%E9%93%BE%E6%8E%A5%E6%81%A2%E5%A4%8D%E7%8E%AF%E5%A2%83%EF%BC%9Ahttps://jimeng.jianying.com/envs/injectors/reset?ignore_env=1&redirect_url=https%253A%252F%252Fjimeng.jianying.com%252Fai-tool%20%0A%0A; ttwid=1|CuszwRz2H2cnQrSIUIZYEnA3Xa_RRpmefRQUffJGJuU|1751283169|bc2487cf40a0f33665ffb5a1fd0f06df8d30c138ea1b1841a3df1169e0c5048a; _uetsid=515ad4f0556c11f08d382364fb45061a; _uetvid=c69ab3902f2b11f0b327efd64212a1d2; odin_tt=241a2accf1da0f82776c273cd3df545707f3be4cf6678ab7d097d40bc231e9ad80315d93b84e6d862d32bfbd74dbd5736a5a2d215b02c0f03793d7856b4dcf8e"},
    { id: Date.now().toString() + "_header_4", key: "pf", value: "7" },
    { id: Date.now().toString() + "_header_5", key: "appid", value: "581595" },
  ];

export const SettingsModal: React.FC<SettingsModalProps> = ({
  isOpen,
  onClose,
}) => {
  // Add state for new LLM parameters
  const [modelName, setModelName] = useState("");
  const [endpoint, setEndpoint] = useState("");
  const [apiKey, setApiKey] = useState("");
  const [systemPrompt, setSystemPrompt] = useState(""); // Reuse defaultPrompt state or create new? Let's create new for clarity
  const [temperature, setTemperature] = useState<number | string>("");
  const [mcpServers, setMcpServers] = useState<McServerEntry[]>([]); // Store as array of objects
  // Host Agent settings
  const [hostAgentUrl, setHostAgentUrl] = useState("");
  const [hostAgentUseProxy, setHostAgentUseProxy] = useState(false);
  const [hostAgentHeaders, setHostAgentHeaders] = useState<
    HostAgentHeaderEntry[]
  >([]);
  const [hostAgentConversationListUrl, setHostAgentConversationListUrl] =
    useState("");
  const [hostAgentConversationItemUrl, setHostAgentConversationItemUrl] =
    useState("");
  const [hostAgentConversationResumeUrl, setHostAgentConversationResumeUrl] =
      useState("");

  const [hostAgentConversationStopUrl, setHostAgentConversationStopUrl] =
      useState("");
  
  const [scene, setScene] = useState("");

  type Tab = "LLM" | "MCP" | "Host Agent" | "Plugins"; // 添加 Plugins Tab
  const [activeTab, setActiveTab] = useState<Tab>("LLM");
  const [plugins, setPlugins] = useState<PluginMetadata[]>([]); // 新增插件状态

  useEffect(() => {
    if (isOpen) {
      // Load new LLM parameters from localStorage
      setModelName(localStorage.getItem("x-llm-model") ?? "");
      setEndpoint(localStorage.getItem("x-llm-endpoint") ?? "");
      setApiKey(localStorage.getItem("x-llm-apikey") ?? "");
      setSystemPrompt(localStorage.getItem("x-llm-systemprompt") ?? "");
      setTemperature(localStorage.getItem("x-llm-temperature") ?? "");
      const storedMcpServers = localStorage.getItem("x-llm-mcpservers");
      if (storedMcpServers) {
        try {
          const parsedServers = JSON.parse(storedMcpServers);
          if (Array.isArray(parsedServers)) {
            setMcpServers(
              parsedServers.map((s, index) => ({
                id: s.id || Date.now().toString() + index, // Ensure ID exists or generate one
                name: s.name || "",
                url: s.url || "",
              }))
            );
          } else {
            setMcpServers([{ id: Date.now().toString(), name: "", url: "" }]);
          }
        } catch (e) {
          console.error("Failed to parse MCP servers from localStorage", e);
          setMcpServers([{ id: Date.now().toString(), name: "", url: "" }]);
        }
      } else {
        setMcpServers([{ id: Date.now().toString(), name: "", url: "" }]);
      }
      // Load Host Agent settings
      setHostAgentUrl(localStorage.getItem("x-hostagent-url") ?? "");
      setHostAgentUseProxy(
        localStorage.getItem("x-hostagent-useproxy") === "true"
      );
      const storedHostAgentHeaders = localStorage.getItem(
        "x-hostagent-headers"
      );

      if (storedHostAgentHeaders) {
        try {
          const parsedHeaders = JSON.parse(storedHostAgentHeaders);
          if (Array.isArray(parsedHeaders)) {
            setHostAgentHeaders(
              parsedHeaders.map((h, index) => ({
                id: h.id || Date.now().toString() + index + "_header",
                key: h.key || "",
                value: h.value || "",
              }))
            );
          } else {
            setHostAgentHeaders([
              { id: Date.now().toString() + "_header", key: "", value: "" },
            ]);
          }
        } catch (e) {
          console.error(
            "Failed to parse Host Agent headers from localStorage",
            e
          );
          setHostAgentHeaders([
            { id: Date.now().toString() + "_header", key: "", value: "" },
          ]);
        }
      } else {
        setHostAgentHeaders([
          { id: Date.now().toString() + "_header", key: "", value: "" },
        ]);
      }
      // Load new Host Agent conversation URLs
      setHostAgentConversationListUrl(
        localStorage.getItem("x-hostagent-conversation-list-url") ?? ""
      );
      setHostAgentConversationItemUrl(
        localStorage.getItem("x-hostagent-conversation-item-url") ?? ""
      );

      setHostAgentConversationResumeUrl(
          localStorage.getItem("x-hostagent-conversation-resume-url") ?? ""
      );

      setHostAgentConversationStopUrl(
          localStorage.getItem("x-hostagent-conversation-stop-url") ?? ""
      );

      setScene(localStorage.getItem("agent-scene") ?? "");

      // 加载插件信息
      setPlugins(PluginCenter.instance.getAllPluginsMetadata());
    }
  }, [isOpen]);

  const handleSave = () => {
    // Save new LLM parameters to localStorage
    localStorage.setItem("x-llm-model", modelName);
    localStorage.setItem("x-llm-endpoint", endpoint);
    localStorage.setItem("x-llm-apikey", apiKey);
    localStorage.setItem("x-llm-systemprompt", systemPrompt);
    localStorage.setItem("x-llm-temperature", String(temperature));
    localStorage.setItem(
      "x-llm-mcpservers",
      JSON.stringify(mcpServers.map(({ name, url }) => ({ name, url })))
    );
    // Save Host Agent settings
    localStorage.setItem("x-hostagent-url", hostAgentUrl);
    localStorage.setItem("x-hostagent-useproxy", String(hostAgentUseProxy));
    localStorage.setItem(
      "x-hostagent-headers",
      JSON.stringify(hostAgentHeaders.map(({ key, value }) => ({ key, value })))
    );
    // Save new Host Agent conversation URLs
    localStorage.setItem(
      "x-hostagent-conversation-list-url",
      hostAgentConversationListUrl
    );
    localStorage.setItem(
      "x-hostagent-conversation-item-url",
      hostAgentConversationItemUrl
    );
    localStorage.setItem(
        "x-hostagent-conversation-resume-url",
        hostAgentConversationResumeUrl
    );

    localStorage.setItem(
        "x-hostagent-conversation-stop-url",
        hostAgentConversationStopUrl
    );
    localStorage.setItem("agent-scene", scene);
    
    
    onClose();
    window.location.reload(); // Reload the page to apply the changes
  };

  const handlePluginToggle = (pluginId: string, enabled: boolean) => {
    if (enabled) {
      PluginCenter.instance.enablePlugin(pluginId);
    } else {
      PluginCenter.instance.disablePlugin(pluginId);
    }
    // 更新本地状态以立即反映UI变化
    setPlugins(PluginCenter.instance.getAllPluginsMetadata());
  };

  // Host Agent Header Handlers
  const handleAddHostAgentHeader = () => {
    setHostAgentHeaders((prev) => [
      ...prev,
      { id: Date.now().toString() + "_header", key: "", value: "" },
    ]);
  };

  const handleRemoveHostAgentHeader = (idToRemove: string) => {
    setHostAgentHeaders((prev) =>
      prev.filter((header) => header.id !== idToRemove)
    );
  };

  const handleHostAgentHeaderChange = (
    idToUpdate: string,
    field: keyof Omit<HostAgentHeaderEntry, "id">,
    value: string
  ) => {
    setHostAgentHeaders((prev) =>
      prev.map((header) =>
        header.id === idToUpdate ? { ...header, [field]: value } : header
      )
    );
  };

  const handleAddMcServer = () => {
    setMcpServers((prev) => [
      ...prev,
      { id: Date.now().toString(), name: "", url: "" },
    ]);
  };

  const handleRemoveMcServer = (idToRemove: string) => {
    setMcpServers((prev) => prev.filter((server) => server.id !== idToRemove));
  };

  const handleMcServerChange = (
    idToUpdate: string,
    field: keyof Omit<McServerEntry, "id">,
    value: string
  ) => {
    setMcpServers((prev) =>
      prev.map((server) =>
        server.id === idToUpdate ? { ...server, [field]: value } : server
      )
    );
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-2xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-semibold text-gray-800">设置</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200"
            aria-label="关闭"
          >
            <svg
              className="w-6 h-6 text-gray-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        <div className="space-y-6">
          {/* New LLM Parameter Inputs */}
          <div className="">
            <div className="mb-4 border-b border-gray-200">
              <nav className="-mb-px flex space-x-8" aria-label="Tabs">
                {["LLM", "MCP", "Host Agent", "Plugins"].map((tabName) => (
                  <button
                    key={tabName}
                    onClick={() => setActiveTab(tabName as Tab)}
                    className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm ${
                      activeTab === tabName
                        ? "border-blue-500 text-blue-600"
                        : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                    }`}
                  >
                    {tabName}
                  </button>
                ))}
              </nav>
            </div>

            {activeTab === "LLM" && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Model Name (x-llm-model)
                  </label>
                  <input
                    type="text"
                    value={modelName}
                    onChange={(e) => setModelName(e.target.value)}
                    className="w-full px-4 py-2 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                    placeholder="e.g., gpt-4, deepseek-r1"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Endpoint (x-llm-endpoint)
                  </label>
                  <input
                    type="text"
                    value={endpoint}
                    onChange={(e) => setEndpoint(e.target.value)}
                    className="w-full px-4 py-2 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                    placeholder="e.g., https://api.example.com/v1"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    API Key (x-llm-apikey)
                  </label>
                  <input
                    type="password" // Use password type for sensitive info
                    value={apiKey}
                    onChange={(e) => setApiKey(e.target.value)}
                    className="w-full px-4 py-2 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                    placeholder="Enter your API key"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    System Prompt (x-llm-systemprompt)
                  </label>
                  <textarea
                    value={systemPrompt}
                    onChange={(e) => setSystemPrompt(e.target.value)}
                    className="w-full h-24 px-4 py-2 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                    placeholder="Enter system prompt here..."
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Temperature (x-llm-temperature)
                  </label>
                  <input
                    type="number"
                    value={temperature}
                    onChange={(e) => setTemperature(e.target.value)}
                    className="w-full px-4 py-2 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                    placeholder="e.g., 0.7 (leave empty for default)"
                    step="0.1"
                  />
                </div>
              </div>
            )}

            {activeTab === "Plugins" && (
              <div className="space-y-4">
                {plugins.length === 0 && (
                  <p className="text-sm text-gray-500">暂无可用插件。</p>
                )}
                {plugins.map((plugin) => (
                  <div
                    key={plugin.id}
                    className="flex items-center justify-between p-3 border border-gray-200 rounded-lg"
                  >
                    <div>
                      <h4 className="font-medium text-gray-800">
                        {plugin.name}
                      </h4>
                      <p className="text-sm text-gray-500">
                        {plugin.description}
                      </p>
                    </div>
                    <label
                      htmlFor={`plugin-toggle-${plugin.id}`}
                      className="flex items-center cursor-pointer"
                    >
                      <div className="relative">
                        <input
                          type="checkbox"
                          id={`plugin-toggle-${plugin.id}`}
                          className="sr-only"
                          checked={plugin.enabled}
                          onChange={(e) => {
                            if (plugin.id === (DreaminaAgentPlugin.id || DreaminaAgent3Plugin.id)  && e.target.checked) {

                              localStorage.setItem("x-hostagent-useproxy","true");
                              setHostAgentUseProxy(true)

                              let x_hostagent_url = "https://jimeng.jianying.com/mweb/v1/creation_agent/v2/conversation"
                              localStorage.setItem("x-hostagent-url",x_hostagent_url);
                              setHostAgentUrl(x_hostagent_url);

                              let list_url = "https://jimeng.jianying.com/mweb/v1/creation_agent/fetch_conversation_list"
                              localStorage.setItem("x-hostagent-conversation-list-url",list_url);
                              setHostAgentConversationListUrl(list_url)

                              let item_url = "https://jimeng.jianying.com/mweb/v1/creation_agent/fetch_conversation"
                              localStorage.setItem("x-hostagent-conversation-item-url",item_url);
                              setHostAgentConversationItemUrl(item_url)

                              let resume_url = "https://jimeng.jianying.com/mweb/v1/creation_agent/v2/resume"
                              localStorage.setItem("x-hostagent-conversation-resume-url",resume_url);
                              setHostAgentConversationResumeUrl(resume_url)

                              let stop_url = "https://jimeng.jianying.com/mweb/v1/creation_agent/v2/stop_stream"
                              localStorage.setItem("x-hostagent-conversation-stop-url",stop_url);
                              setHostAgentConversationStopUrl(stop_url)

                            }
                            handlePluginToggle(plugin.id, e.target.checked)
                           }
                          }
                        />
                        <div className="block bg-gray-200 w-10 h-6 rounded-full"></div>
                        <div
                          className={`dot absolute left-1 top-1 bg-white w-4 h-4 rounded-full transition-transform ${
                            plugin.enabled
                              ? "translate-x-full !bg-blue-500"
                              : ""
                          }`}
                        ></div>
                      </div>
                    </label>
                  </div>
                ))}
              </div>
            )}

            {activeTab === "MCP" && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    MCP Servers (x-llm-mcpservers)
                  </label>
                  {mcpServers.map((server, index) => (
                    <div
                      key={server.id}
                      className="space-y-2 mb-4 p-3 border border-gray-200 rounded-lg"
                    >
                      <div className="flex items-center space-x-2">
                        <input
                          type="text"
                          value={server.name}
                          onChange={(e) =>
                            handleMcServerChange(
                              server.id,
                              "name",
                              e.target.value
                            )
                          }
                          className="w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                          placeholder="Server Name (e.g., server1)"
                        />
                        <input
                          type="text"
                          value={server.url}
                          onChange={(e) =>
                            handleMcServerChange(
                              server.id,
                              "url",
                              e.target.value
                            )
                          }
                          className="w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                          placeholder="Server URL (e.g., http://localhost:8080)"
                        />
                        <button
                          onClick={() => handleRemoveMcServer(server.id)}
                          className="p-2 text-red-500 hover:text-red-700 hover:bg-red-100 rounded-md transition-colors duration-200"
                          aria-label="Remove Server"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-5 w-5"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path
                              fillRule="evenodd"
                              d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </button>
                      </div>
                    </div>
                  ))}
                  <button
                    onClick={handleAddMcServer}
                    className="mt-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200 text-sm"
                  >
                    Add Server
                  </button>
                </div>
              </div>
            )}

            {activeTab === "Host Agent" && (
              <div className="space-y-4">
                <div className="flex items-center">
                  <input
                    id="hostAgentUseProxy"
                    type="checkbox"
                    checked={hostAgentUseProxy}
                    onChange={(e) => setHostAgentUseProxy(e.target.checked)}
                    className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <label
                    htmlFor="hostAgentUseProxy"
                    className="ml-2 block text-sm text-gray-900"
                  >
                    Use Proxy (x-hostagent-useproxy)
                  </label>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Host Agent URL (x-hostagent-url)
                  </label>
                  <input
                    type="text"
                    value={hostAgentUrl}
                    onChange={(e) => setHostAgentUrl(e.target.value)}
                    className="w-full px-4 py-2 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                    placeholder="e.g., http://localhost:8000"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Conversation List URL (x-hostagent-conversation-list-url)
                  </label>
                  <input
                    type="text"
                    value={hostAgentConversationListUrl}
                    onChange={(e) =>
                      setHostAgentConversationListUrl(e.target.value)
                    }
                    className="w-full px-4 py-2 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                    placeholder="e.g., /conversations"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Conversation Item URL (x-hostagent-conversation-item-url)
                  </label>
                  <input
                    type="text"
                    value={hostAgentConversationItemUrl}
                    onChange={(e) =>
                      setHostAgentConversationItemUrl(e.target.value)
                    }
                    className="w-full px-4 py-2 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                    placeholder="e.g., /conversation/{id}"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Conversation Resume URL (x-hostagent-conversation-resume-url)
                  </label>
                  <input
                      type="text"
                      value={hostAgentConversationResumeUrl}
                      onChange={(e) =>
                          setHostAgentConversationResumeUrl(e.target.value)
                      }
                      className="w-full px-4 py-2 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                      placeholder="e.g., /conversation/{id}"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Conversation Stop URL (x-hostagent-conversation-stop-url)
                  </label>
                  <input
                      type="text"
                      value={hostAgentConversationStopUrl}
                      onChange={(e) =>
                          setHostAgentConversationStopUrl(e.target.value)
                      }
                      className="w-full px-4 py-2 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                      placeholder="e.g., /conversation/{id}"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Scene
                  </label>
                  <input
                      type="text"
                      value={scene}
                      onChange={(e) =>
                          setScene(e.target.value)
                      }
                      className="w-full px-4 py-2 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                      placeholder="e.g., scene_infra_agent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Host Agent Headers (x-hostagent-headers)
                  </label>
                  {hostAgentHeaders.map((header, index) => (
                    <div
                      key={header.id}
                      className="space-y-2 mb-4 p-3 border border-gray-200 rounded-lg"
                    >
                      <div className="flex items-center space-x-2">
                        <input
                          type="text"
                          value={header.key}
                          onChange={(e) =>
                            handleHostAgentHeaderChange(
                              header.id,
                              "key",
                              e.target.value
                            )
                          }
                          className="w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                          placeholder="Header Name (e.g., Authorization)"
                        />
                        <input
                          type="text"
                          value={header.value}
                          onChange={(e) =>
                            handleHostAgentHeaderChange(
                              header.id,
                              "value",
                              e.target.value
                            )
                          }
                          className="w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                          placeholder="Header Value (e.g., Bearer token)"
                        />
                        <button
                          onClick={() => handleRemoveHostAgentHeader(header.id)}
                          className="p-2 text-red-500 hover:text-red-700 hover:bg-red-100 rounded-md transition-colors duration-200"
                          aria-label="Remove Header"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-5 w-5"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path
                              fillRule="evenodd"
                              d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </button>
                      </div>
                    </div>
                  ))}
                  <button
                    onClick={handleAddHostAgentHeader}
                    className="mt-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200 text-sm"
                  >
                    Add Header
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="mt-8 flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-6 py-2 border border-gray-200 text-gray-700 rounded-xl hover:bg-gray-50 transition-colors duration-200"
          >
            取消
          </button>
          <button
            onClick={handleSave}
            className="px-6 py-2 bg-blue-500 text-white rounded-xl hover:bg-blue-600 transition-colors duration-200"
          >
            保存
          </button>
        </div>
      </div>
    </div>
  );
};
