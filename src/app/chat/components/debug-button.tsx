"use client";

import React, { useState } from "react";

interface DebugButtonProps {
  logId?: string;
  onArgosClick?: (logId: string) => void;
  onFornaxClick?: (logId: string) => void;
}

export const DebugButton: React.FC<DebugButtonProps> = ({ logId, onArgosClick, onFornaxClick }) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 flex items-center justify-end w-full"
        aria-label="调试"
      >
        <svg className="w-6 h-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
      </button>

      {isOpen && (
        <div className="fixed inset-0 flex items-center justify-center z-50" style={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}>
          <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6 w-auto min-w-[500px] max-w-2xl">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">Debug Message</h3>
              <button
                onClick={() => setIsOpen(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <svg
                  className="w-6 h-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>
            <div className="space-y-4">
              <div className="flex items-center">
                <span className="font-semibold w-20">logId</span>
                <span className="text-gray-700 font-mono text-sm mr-4">{logId}</span>
                <a
                  href={logId ? `https://cloud.bytedance.net/argos/trace/retrieve/logIdRetrieve?curTimeShift=600&data_source_uid=**********&end=&expired_time=&isCustomTime=false&logId=${logId}&log_search=true&page=log&psm=videocut.dreamina.agent_core&psmList=videocut.dreamina.agent_core&region=cn&start=&timeSpan=10&trace_search=true&type=__logid&x-bc-region-id=bytedance&x-resource-account=public` : '#'}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="px-3 py-1 text-blue-600 hover:underline cursor-pointer"
                >
                  Argos
                </a>
                <a
                  href={logId ? `https://fornax.bytedance.net/space/7527962177773240321/analytics/log-query?env=online&logIdTime=4&queryID=${logId}&queryType=LogID` : '#'}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="px-3 py-1 text-blue-600 hover:underline cursor-pointer"
                >
                  Fornax
                </a>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};