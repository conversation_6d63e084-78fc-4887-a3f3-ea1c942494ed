"use client";

import React from "react";
import Link from "next/link";

export const NewConversationButton: React.FC = () => {
  return (
    <a
      onClick={() => {
        if (window.location.href.indexOf("conversation_id") > 0) {
          window.location.href = "/";
        } else {
          window.location.reload();
        }
      }}
    >
      <button
        className="fixed top-4 right-52 px-4 py-2 text-black rounded-lg flex items-center gap-2"
        aria-label="创建新对话"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-6 w-6 text-gray-600"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
          strokeWidth={2}
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="M12 4v16m8-8H4"
          />
        </svg>
        创建新对话
      </button>
    </a>
  );
};
