"use client";

import React from "react";
import {
  dumpHostAgentOptionsToTOS,
  initializeHostAgentOptions,
} from "../utils/hostAgentOptionsHelper";
import { PluginCenter } from "../plugin-system/plugin-center";

interface ShareButtonProps {
  conversationId: string;
  uid: string;
}

const copyToClipboard = (text: string) => {
  const input = document.createElement("input");
  input.style.position = "fixed";
  input.style.opacity = "0";
  input.value = text;
  document.body.appendChild(input);
  input.select();
  try {
    document.execCommand("copy");
    alert("分享链接已复制到剪贴板");
  } catch (err) {
    console.error("复制失败:", err);
    window.open(text, "_blank");
  }
  document.body.removeChild(input);
};

export function ShareButton({ conversationId, uid }: ShareButtonProps) {
  const encodePlugins = PluginCenter.instance
    .getAllPluginsMetadata()
    .filter((plugin) => plugin.enabled)
    .map((plugin) => plugin.id)
    .join(",");
  const handleShare = async () => {
    if (conversationId) {
      const hostAgentOptions = initializeHostAgentOptions();
      if (hostAgentOptions["x-hostagent-url"] && uid !== "tos") {
        await dumpHostAgentOptionsToTOS(conversationId);
        const shareUrl = `${window.location.origin}/?conversation_id=${conversationId}&uid=tos&plugins=${encodePlugins}`;
        copyToClipboard(shareUrl);
        return;
      }
      const shareUrl = `${window.location.origin}/?conversation_id=${conversationId}&uid=${uid}&plugins=${encodePlugins}`;
      copyToClipboard(shareUrl);
    }
  };

  return (
    <button
      onClick={handleShare}
      className="fixed top-4 right-24 px-4 py-2 text-black rounded-lg flex items-center gap-2"
    >
      <svg
        className="w-4 h-4"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"
        />
      </svg>
      分享对话
    </button>
  );
}
