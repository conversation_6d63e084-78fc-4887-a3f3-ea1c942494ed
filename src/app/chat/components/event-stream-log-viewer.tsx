import React, { useState, useEffect, useRef } from 'react';

interface EventStreamLogViewerProps {
  conversationId?: string;
}{
  // Props if any, for now, it's self-contained with event listeners
}

const EventStreamLogViewer: React.FC<EventStreamLogViewerProps> = ({ conversationId }) => {
  // Use the passed conversationId or a default if not provided
  const downloadConversationId = conversationId || "unknown_conversation"; 

  const handleDownloadLogs = () => {
    const logContent = logs.join('\n');
    const blob = new Blob([logContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${downloadConversationId}.log.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const [logs, setLogs] = useState<string[]>([]);
  const logsEndRef = useRef<null | HTMLDivElement>(null);

  const scrollToBottom = () => {
    logsEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    const handleNewLog = (event: CustomEvent<string>) => {
      setLogs((prevLogs) => [...prevLogs, event.detail]);
    };

    document.addEventListener('nuroEventStreamChunk', handleNewLog as EventListener);

    return () => {
      document.removeEventListener('nuroEventStreamChunk', handleNewLog as EventListener);
    };
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [logs]);

  return (
    <div style={{ 
      height: '100%', 
      display: 'flex',
      flexDirection: 'column',
      overflowY: 'auto', 
      border: '1px solid #ccc', 
      padding: '10px', 
      fontFamily: 'monospace', 
      whiteSpace: 'pre-wrap', 
      backgroundColor: '#f5f5f5'
    }}>
      <button 
        onClick={handleDownloadLogs} 
        style={{
          alignSelf: 'flex-end',
          marginBottom: '10px',
          padding: '5px 10px',
          cursor: 'pointer'
        }}
      >
        Download Logs
      </button>
      <div style={{ flexGrow: 1, overflowY: 'auto' }}>
      {logs.map((log, index) => (
        <div key={index}>{log}</div>
      ))}
        <div ref={logsEndRef} />
      </div>
    </div>
  );
};

export default EventStreamLogViewer;