import crypto from "crypto";

export class Utils {
  static appId = "581595";

  static randomUUIDString(): string {
    return "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx".replace(
      /[xy]/g,
      function (c) {
        var r = (Math.random() * 16) | 0,
          v = c == "x" ? r : (r & 0x3) | 0x8;
        return v.toString(16);
      },
    );
  }

  // for server use only
  static md5(str: string): string {
    return crypto.createHash("md5").update(str).digest("hex");
  }
}
