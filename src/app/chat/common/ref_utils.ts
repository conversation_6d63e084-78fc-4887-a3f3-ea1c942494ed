import {
  NuroToolCallMessage,
  NuroAssistantMessage,
  NuroCanvasMessage,
  RefContent,
  NuroReasoningMessage, // Make sure RefContent is imported
  // Potentially other types if needed for specific content extraction
} from '@byted/nurosdk-js';

// Function to convert NuroAssistantMessage to RefContent[]
export function assistantMessageToRefContent(message: NuroAssistantMessage): RefContent[] {
  const refContents: RefContent[] = [];
  if (message.text) {
    refContents.push({
      text: message.text,
      // You might want to add a messageId or other identifiers if needed
      // id: message.id 
    });
  }
  // If assistant messages can contain files or other structured data suitable for reference,
  // add logic here to extract and convert them.
  return refContents;
}

// Function to convert NuroToolCallMessage to RefContent[]
export function toolCallMessageToRefContent(message: NuroToolCallMessage): RefContent[] {
  const refContents: RefContent[] = [];
  // Example: Use tool name and arguments as reference text
  let textContent = `Tool: ${message.toolName}`;
  if (message.toolArgs) {
    textContent += `\nArgs: ${message.toolArgs}`;
  }
  refContents.push({
    text: textContent,
  });

  // If tool calls have specific outputs that should be referenced as files (e.g., an image generated)
  // you would need to access that information. Assuming toolResult might contain such info.
  // This is highly dependent on how your tool results are structured.
  // Example (conceptual):
  // if (message.toolResult?.imageUrl) {
  //   refContents.push({
  //     type: 'file',
  //     text: `Generated image from ${message.toolName}`,
  //     imageUrl: message.toolResult.imageUrl,
  //     // id: message.id + '_image'
  //   });
  // }
  return refContents;
}

// Function to convert NuroCanvasMessage to RefContent[]
export function canvasMessageToRefContent(message: NuroCanvasMessage): RefContent[] {
  const refContents: RefContent[] = [];

  // Example: Reference the canvas operation type or a summary
  if (message.nodes && message.nodes.length > 0) {
    // You could iterate through nodes and create more specific RefContent items
    message.nodes.forEach((node, index) => {
        let textContent = `Canvas tool: ${node.toolName}`;

        if (node.toolArgs) {
            textContent += `\nArgs: ${node.toolArgs}`;
          }
        refContents.push({
            text: textContent,
        });
      // Example: if a node has an image URL
    });
  }
  

  return refContents;
}

export function reasoningMessageToRefContent(message:NuroReasoningMessage): RefContent[] {
    const refContents: RefContent[] = [];
  if (message.text) {
    refContents.push({
      text: message.text,
      // You might want to add a messageId or other identifiers if needed
      // id: message.id 
    });
  }
  // If assistant messages can contain files or other structured data suitable for reference,
  // add logic here to extract and convert them.
  return refContents;
}

// You might also want a generic function that inspects the message type
export function nuroMessageToRefContent(message: NuroAssistantMessage | NuroToolCallMessage | NuroCanvasMessage | NuroReasoningMessage): RefContent[] {
  if (message instanceof NuroAssistantMessage) {
    return assistantMessageToRefContent(message);
  }
  if (message instanceof NuroToolCallMessage) {
    return toolCallMessageToRefContent(message);
  }
  if (message instanceof NuroCanvasMessage) {
    return canvasMessageToRefContent(message);
  }
  if (message instanceof NuroReasoningMessage) {
    return reasoningMessageToRefContent(message);
  }
  return []; // Should not happen if type is one of the above
}