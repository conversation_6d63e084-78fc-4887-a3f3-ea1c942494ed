import { atom } from 'jotai';

// 消息ID到logId的映射原子
export const messageLogMapAtom = atom<Record<string, string>>({});

// 最近的chat_proxy请求的logId缓存
export const latestLogIdAtom = atom<string>('');

// 设置最新logId的action atom
export const setLatestLogIdAtom = atom(
  null,
  (get, set, logId: string) => {
    console.log(`Setting latest logId: ${logId}`);
    set(latestLogIdAtom, logId);
  }
);

// 关联消息ID与logId的action atom（在消息完成时调用）
export const associateMessageLogIdAtom = atom(
  null,
  (get, set, messageId: string) => {
    const latestLogId = get(latestLogIdAtom);
    console.log(`Attempting to associate: messageId=${messageId}, latestLogId=${latestLogId}`);
    if (latestLogId && messageId) {
      const currentMap = get(messageLogMapAtom);
      set(messageLogMapAtom, { ...currentMap, [messageId]: latestLogId });
      console.log(`Successfully associated: ${messageId} -> ${latestLogId}`);
    } else {
      console.log(`Failed to associate: missing logId or messageId`);
    }
  }
);

// 获取logId的读取atom
export const getLogIdAtom = atom(
  (get) => (messageId: string) => {
    const logMap = get(messageLogMapAtom);
    return logMap[messageId] || '';
  }
);

// 清理所有数据的action atom
export const resetLogStoreAtom = atom(
  null,
  (get, set) => {
    set(messageLogMapAtom, {});
    set(latestLogIdAtom, '');
  }
);