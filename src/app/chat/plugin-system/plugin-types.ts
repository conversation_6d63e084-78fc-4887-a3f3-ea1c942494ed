import { NuroMCPServerConfig, NuroTask<PERSON>hecker, NuroToolCallMessage, RefContent } from "@byted/nurosdk-js";
import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import React from "react";
import { WithReferenceProps } from "../nuro-components/with-reference";

// 定义插件可以注册的能力类型
export enum PluginCapability {
  CustomTaskChecker = "CustomTaskChecker",
  CustomMCPServer = "CustomMCPServer",
  CustomToolCallCell = "CustomToolCallCell",
}

// 定义 ToolCallCell 渲染器的接口
export interface ToolCallCellRendererProps {
  message: NuroToolCallMessage;
  onReferenceSingleImage?: (message: RefContent[]) => void;
};

// export type ToolCallCellRenderer = React.FC<ToolCallCellRendererProps>;

// 新的类型定义：一个函数，接收消息，返回一个渲染器组件或 undefined
export type ToolCallCellRendererProvider = (
  message: NuroToolCallMessage
) => React.FC<ToolCallCellRendererProps & WithReferenceProps> | undefined;

export type MCPServerProvider = () => NuroMCPServerConfig[];

// 定义插件接口
export interface Plugin {
  id: string; // 插件的唯一标识符
  name: string; // 插件的显示名称
  description?: string; // 插件的描述
  version: string; // 插件版本

  // 插件提供的能力
  capabilities: {
    [PluginCapability.CustomTaskChecker]?: () => NuroTaskChecker;
    [PluginCapability.CustomMCPServer]?: MCPServerProvider;
    [PluginCapability.CustomToolCallCell]?: ToolCallCellRendererProvider;
  };

  // 插件生命周期方法 (可选)
  onLoad?: () => void; // 插件加载时调用
  onUnload?: () => void; // 插件卸载时调用
}

// 定义插件元数据，用于在设置中显示和管理
export interface PluginMetadata {
  id: string;
  name: string;
  description?: string;
  version: string;
  enabled: boolean; // 插件是否启用
}
