"use client";

import {
  <PERSON>uroMCPServer<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  NuroToolCallMessage,
} from "@byted/nurosdk-js";
import {
  Plugin,
  PluginCapability,
  ToolCallCellRendererProps, // Added import for the props type
  PluginMetadata,
} from "./plugin-types";
import { WithReferenceProps } from "../nuro-components/with-reference";

export class PluginCenter {
  static instance: PluginCenter = new PluginCenter();

  private registeredPlugins: Map<string, Plugin> = new Map();
  private enabledPluginIds: Set<string> = new Set();

  constructor() {
    this.loadEnabledPluginsConfig();
  }

  registerPlugin(plugin: Plugin): void {
    if (this.registeredPlugins.has(plugin.id)) {
      console.warn(
        `Plugin with id "${plugin.id}" is already registered. Skipping.`
      );
      return;
    }
    this.registeredPlugins.set(plugin.id, plugin);
    if (plugin.onLoad) {
      plugin.onLoad();
    }
    console.log(`Plugin "${plugin.name}" (id: ${plugin.id}) registered.`);
  }

  unregisterPlugin(pluginId: string): void {
    const plugin = this.registeredPlugins.get(pluginId);
    if (plugin) {
      if (plugin.onUnload) {
        plugin.onUnload();
      }
      this.registeredPlugins.delete(pluginId);
      this.enabledPluginIds.delete(pluginId);
      this.saveEnabledPluginsConfig();
      console.log(`Plugin "${plugin.name}" (id: ${plugin.id}) unregistered.`);
    } else {
      console.warn(
        `Plugin with id "${pluginId}" not found for unregistration.`
      );
    }
  }

  getPlugin(pluginId: string): Plugin | undefined {
    return this.registeredPlugins.get(pluginId);
  }

  getAllPluginsMetadata(): PluginMetadata[] {
    const metadata: PluginMetadata[] = [];
    this.registeredPlugins.forEach((plugin) => {
      metadata.push({
        id: plugin.id,
        name: plugin.name,
        description: plugin.description,
        version: plugin.version,
        enabled: this.isPluginEnabled(plugin.id),
      });
    });
    return metadata;
  }

  enablePlugin(pluginId: string): void {
    if (this.registeredPlugins.has(pluginId)) {
      this.enabledPluginIds.add(pluginId);
      this.saveEnabledPluginsConfig();
      console.log(`Plugin with id "${pluginId}" enabled.`);
    } else {
      console.warn(
        `Cannot enable plugin with id "${pluginId}". Plugin not registered.`
      );
    }
  }

  disablePlugin(pluginId: string): void {
    this.enabledPluginIds.delete(pluginId);
    this.saveEnabledPluginsConfig();
    console.log(`Plugin with id "${pluginId}" disabled.`);
  }

  isPluginEnabled(pluginId: string): boolean {
    if (typeof window !== "undefined") {
      const urlPlugins = new URLSearchParams(window.location.search).get(
        "plugins"
      );
      if (urlPlugins) {
        const enabledPlugins = urlPlugins.split(",");
        return enabledPlugins.includes(pluginId);
      }
    }
    return this.enabledPluginIds.has(pluginId);
  }

  getTaskChecker(): NuroTaskChecker | undefined {
    const checkers: NuroTaskChecker[] = [];
    this.registeredPlugins.forEach((plugin) => {
      if (!this.isPluginEnabled(plugin.id)) {
        return;
      }
      const checker =
        plugin.capabilities?.[PluginCapability.CustomTaskChecker]?.();
      if (checker) {
        checkers.push(checker);
      }
    });
    return checkers[0];
  }

  getMCPServers(): NuroMCPServerConfig[] {
    const servers: NuroMCPServerConfig[] = [];
    this.registeredPlugins.forEach((plugin) => {
      if (!this.isPluginEnabled(plugin.id)) {
        return;
      }
      const mcpServers =
        plugin.capabilities?.[PluginCapability.CustomMCPServer]?.();
      if (mcpServers) {
        servers.push(...mcpServers);
      }
    });
    return servers;
  }

  // 获取特定能力的实现，例如 ToolCallCellRenderer
  getToolCallCellRenderer(
    message: NuroToolCallMessage
  ): React.FC<ToolCallCellRendererProps & WithReferenceProps> | null | undefined {
    const plugins = Array.from(this.registeredPlugins.values());
    for (let index = 0; index < plugins.length; index++) {
      const plugin = plugins[index];
      if (!this.isPluginEnabled(plugin.id)) {
        continue;
      }
      const provider =
        plugin?.capabilities?.[PluginCapability.CustomToolCallCell];
      if (provider) {
        const CustomCell = provider(message);
        if (CustomCell) {
          // 返回第一个找到的已启用插件提供的渲染器
          // 未来可以考虑更复杂的优先级或选择逻辑
          return CustomCell;
        }
      }
    }
    return undefined;
  }

  private loadEnabledPluginsConfig(): void {
    if (typeof window !== "undefined" && window.localStorage) {
      try {
        const storedConfig = localStorage.getItem("enabledNuroPlugins");
        if (storedConfig) {
          const enabledIds = JSON.parse(storedConfig);
          if (Array.isArray(enabledIds)) {
            this.enabledPluginIds = new Set(
              enabledIds.filter((id) => typeof id === "string")
            );
          }
        }
      } catch (error) {
        console.error("Failed to load enabled plugins configuration:", error);
      }
    }
  }

  private saveEnabledPluginsConfig(): void {
    if (typeof window !== "undefined" && window.localStorage) {
      try {
        localStorage.setItem(
          "enabledNuroPlugins",
          JSON.stringify(Array.from(this.enabledPluginIds))
        );
      } catch (error) {
        console.error("Failed to save enabled plugins configuration:", error);
      }
    }
  }
}
