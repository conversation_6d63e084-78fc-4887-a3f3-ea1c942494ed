import {
  EventStreamAdapter,
  EventStreamConfig,
  NuroSetting,
  TOSFileUploadAdapter,
  TOSFileUploadConfig,
} from "@byted/nurosdk-js";
import { uploaderEndpoint } from "./config";
import { PluginCenter } from "./plugin-system/plugin-center";
import FooWeatherPlugin from "./plugins/foo-weather";
import DreaminaAgentPlugin from "./plugins/dreamina-agent";
import DreaminaAgent3Plugin from "./plugins/dreamina-agent3";
import { Axios } from "axios";
import { Utils } from "./utils";
import { initializeHostAgentOptions } from "./utils/hostAgentOptionsHelper";
import BytedUploader, { UploadResultImageInfo } from "@byted/uploader";
import { closeCanvasName, image2imageV3Name, image2videoV3Name, openNewCanvasName, text2imageV3Name, text2videoV3Name, start2EndVideoV3Name, ref2ImageV3Name } from "@byted/dreamina_dml_js_web";

export const installPlugins = () => {
  PluginCenter.instance.registerPlugin(FooWeatherPlugin);
  PluginCenter.instance.registerPlugin(DreaminaAgentPlugin);
  PluginCenter.instance.registerPlugin(DreaminaAgent3Plugin);

};

export const installEnv = () => {
  NuroSetting.needDisplayServerFunctionMessage = true;
  NuroSetting.version = "3.0.0";
  NuroSetting.canvasSettings = {
    startNode: openNewCanvasName,
    endNode: closeCanvasName,
    nodes: [
      text2imageV3Name,
      image2imageV3Name,
      text2videoV3Name,
      image2videoV3Name,
      start2EndVideoV3Name,
      ref2ImageV3Name,
    ]
  };
  NuroSetting.shieldToolCall = ["handoff_to_planner", "handoff_to_host"];

  const activeRequests = new Map<string, AbortController>();
  const activeStreamConfig = new Map<string, EventStreamConfig>();

  EventStreamAdapter.fetch = (config: EventStreamConfig): string => {
    console.log(`EventStreamAdapter.fetch : ${config.endpoint}`);

    const controller = new AbortController();
    const requestId =
      Date.now().toString() + Math.random().toString(36).substring(2);
    activeRequests.set(requestId, controller);
    activeStreamConfig.set(requestId, config);

    const hostAgentOptions = initializeHostAgentOptions();
    if (
      hostAgentOptions["x-hostagent-url"] &&
      hostAgentOptions["x-hostagent-useproxy"] &&
      config.endpoint !== "/api/chat_proxy"
    ) {
      config.headers = {
        ...(config.headers ?? {}),
        ...hostAgentOptions,
        "x-url": config.endpoint,
        "content-type": "application/json",
      };
      config.endpoint = "/api/chat_proxy";
    }

    if (config.data) {
      const json = JSON.parse(config.data);
      json.scene = localStorage.getItem("agent-scene");
      config.data = JSON.stringify(json);
    }
    
    fetch(config.endpoint, {
      signal: controller.signal,
      method: "POST",
      headers: { ...config.headers },
      body: config.data,
      credentials: "include",
    })
      .then(async (res: any) => {
        // 获取x-tt-logid响应头并通过日志流输出
        const xTtLogid = res.headers.get("x-tt-logid");
          if (xTtLogid) {
            config.onStart?.();
            const logIdChunk = `\n[SYSTEM] X-TT-LogID: ${xTtLogid}\n\n`;
            const event = new CustomEvent("nuroEventStreamChunk", {
              detail: logIdChunk,
            });
            console.log("receive xTtLogId " + xTtLogid)
            document.dispatchEvent(event);
          }
        if (res.ok) {
          let decoder = new TextDecoderStream();
          let iter = res.body.pipeThrough(decoder);
          let reader = iter.getReader();
          for (;;) {
            let chunk = await reader.read();
            if (typeof chunk.value === "string") {
              config.onChunk?.(chunk.value);
              // Dispatch custom event for the log viewer
              const event = new CustomEvent("nuroEventStreamChunk", {
                detail: chunk.value,
              });
              document.dispatchEvent(event);
            }
            if (chunk.done) {
              break;
            }
          }
          config.onFinish?.();
        } else {
          const reason = await res.text();
          // 使用正则表达式去除 HTML 标签
          const cleanReason = reason.replace(/<[^>]*>/g, "");
          alert(cleanReason);
          config.onError?.(res.code, cleanReason);
        }
      })
      .catch((error) => {
        if (error.name === "AbortError") {
          console.log("Fetch aborted");
          // config.onError?.(-2, "request cancelled"); // Or handle as a specific cancellation error
        } else {
          config.onError?.(-1, "fetch failed");
        }
      })
      .finally(() => {
        activeRequests.delete(requestId);
        activeStreamConfig.delete(requestId);
      });
    console.log("requestIdrequestId", requestId);
    return requestId;
  };

  EventStreamAdapter.cancel = (token: string) => {
    console.log("cancel request: " + token);
    const controller = activeRequests.get(token);
    const streamConfig = activeStreamConfig.get(token);
    if (controller) {
      controller.abort();
      streamConfig?.onCancel?.(-1, "user cancel");
      activeRequests.delete(token);
    } else {
      console.warn(`No active request found for token: ${token}`);
    }
  };


  EventStreamAdapter.reconnectEndpoint = localStorage.getItem("x-hostagent-conversation-resume-url") ?? ""
  EventStreamAdapter.interruptEndpoint = localStorage.getItem("x-hostagent-conversation-stop-url") ?? ""

  console.log("reconnectEndpoint:",EventStreamAdapter.reconnectEndpoint,"interruptEndpoint:",EventStreamAdapter.interruptEndpoint)


  /**
   * 这里会根据不同的 HostAgent Endpoint 选择不同的上传方式。
   * @param config
   * @returns
   */
  TOSFileUploadAdapter.upload = (config: TOSFileUploadConfig): string => {
    const hostAgentOptions = initializeHostAgentOptions();
    if (
      (hostAgentOptions["x-hostagent-url"]?.indexOf("jimeng.jianying.com") ??
        -1) !== -1
    ) {
      DreaminaTOSUploader.uploadImageByFile(config.localFile.localFileObject)
        .then((res) => {
          config.nuroFile.url = res.url;
          config.nuroFile.uri = res.uri;
          config.onFinish?.();
        })
        .catch((error) => {
          config.onError?.(-1, "upload failed");
        });
      return "";
    }
    if (hostAgentOptions["x-hostagent-url"]?.indexOf("capcut") ?? -1 !== -1) {
      CapCutTOSUploader.uploadImageByFile(config.localFile.localFileObject)
        .then((res) => {
          config.nuroFile.url = res.PreviewURL;
          config.nuroFile.uri = res.URI;
          config.onFinish?.();
        })
        .catch((error) => {
          config.onError?.(-1, "upload failed");
        });
      return "";
    }
    (() => {
      const reader = new FileReader();
      reader.onload = () => {
        const base64String = reader.result as string;
        const base64Data = base64String.split(",")[1];
        fetch(uploaderEndpoint + "/upload", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            filename: config.localFile.localPath,
            filedata: base64Data,
          }),
        })
          .then(async (res) => {
            if (res.ok) {
              try {
                const url = await res.text();
                config.nuroFile.url = uploaderEndpoint + url;
                config.onFinish?.();
              } catch (error) {
                config.onError?.(-1, "upload failed");
              }
            } else {
              config.onError?.(-1, "upload failed");
            }
          })
          .catch((error) => {
            config.onError?.(-1, "upload failed");
          });
      };
      reader.onerror = (error) => {
        config.onError?.(-1, "upload failed");
      };
      if (config.localFile.localFileObject instanceof File) {
        reader.readAsDataURL(config.localFile.localFileObject);
      } else {
        config.onError?.(-1, "upload failed");
      }
    })();
    return "";
  };
};

interface DreaminaImageUploaderInfo {
  space_name: string;
  space_type: number;
  secret_access_key: string;
  region: string;
  session_token: string;
  upload_domain: string;
  access_key_id: string;
  current_time: string;
  expired_time: string;
}

class DreaminaTOSUploader {
  static commonHeaders = {
    Accept: "application/json, text/plain, */*",
    "User-Agent":
      "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36",
    "sec-ch-ua-platform": "macOS",
    "sec-ch-ua":
      '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
    Referer: "https://jimeng.jianying.com/ai-tool/image/generate",
    pf: "7",
    "sec-ch-ua-mobile": "?0",
    "sign-ver": "1",
    appvr: "5.8.0",
  };

  static async uploadImageByFile(
    file: File
  ): Promise<{ uri: string; url: string; width: number; height: number }> {
    return new Promise(async (resolve, reject) => {
      try {
        const uploaderInfo = await this.getUploaderInfo();
        const bytedUploader = new BytedUploader({
          userId: "unknown",
          appId: 513695,
          imageHost: "https://" + uploaderInfo.upload_domain + "/",
          imageConfig: {
            serviceId: uploaderInfo.space_name,
          },
          objectConfig: {
            serviceId: uploaderInfo.space_name,
          },
        });

        const fileKey = bytedUploader.addFile({
          file: file,
          stsToken: {
            CurrentTime: uploaderInfo.current_time,
            ExpiredTime: uploaderInfo.expired_time,
            SessionToken: uploaderInfo.session_token,
            AccessKeyId: uploaderInfo.access_key_id,
            SecretAccessKey: uploaderInfo.secret_access_key,
          },
          type: "image",
        });
        bytedUploader.on("complete", async (info) => {
          resolve({
            uri: info.oid,
            url: await this.convertUriToUrl(info.oid),
            width: (info.uploadResult as UploadResultImageInfo).ImageWidth ?? 0,
            height:
              (info.uploadResult as UploadResultImageInfo).ImageHeight ?? 0,
          });
        });
        bytedUploader.on("error", (error) => {
          console.error(error);
          reject("图片上传失败");
        });
        bytedUploader.start(fileKey);
      } catch (error) {
        console.error(error);
        reject("图片上传失败");
      }
    });
  }

  static async getUploaderInfo() {
    const hostAgentOptions = initializeHostAgentOptions();
    let axios = new Axios({});
    const uri = "https://jimeng.jianying.com/mweb/v1/get_upload_token";
    const deviceTime = Date.now().toString();
    const sign = Utils.md5(`9e2c|${uri.slice(-7)}|7|5.8.0|${deviceTime}||11ac`);
    const authTokenResponse = await axios.post(
      "/api/chat_proxy",
      JSON.stringify({
        scene: 2,
      }),
      {
        headers: {
          "x-url": uri,
          "Content-Type": "application/json",
          "device-time": deviceTime,
          sign: sign,
          ...hostAgentOptions,
          ...this.commonHeaders,
        },
        responseType: "text",
      }
    );
    const authTokenData = JSON.parse(JSON.parse(authTokenResponse.data)).data;
    return authTokenData as DreaminaImageUploaderInfo;
  }

  static async convertUriToUrl(imageUri: string): Promise<string> {
    const hostAgentOptions = initializeHostAgentOptions();
    let axios = new Axios({});
    const uri = "https://jimeng.jianying.com/mweb/v1/mpack_image";
    const deviceTime = Date.now().toString();
    const sign = Utils.md5(`9e2c|${uri.slice(-7)}|7|5.8.0|${deviceTime}||11ac`);
    const response = await axios.post(
      "/api/chat_proxy",
      JSON.stringify({
        src: [
          {
            image_uri: imageUri,
          },
        ],
        requires: [
          {
            image_uri: imageUri,
          },
        ],
      }),
      {
        headers: {
          "x-url": uri,
          "Content-Type": "application/json",
          "device-time": deviceTime,
          sign: sign,
          ...hostAgentOptions,
          ...this.commonHeaders,
        },
        responseType: "text",
      }
    );
    const data = JSON.parse(JSON.parse(response.data)).data as any;
    if (data?.uri2images?.[imageUri]?.[0]) {
      return data.uri2images[imageUri][0].image_url;
    }
    throw "image info not found.";
  }
}

class CapCutTOSUploader {
  static async uploadImageByFile(file: File): Promise<{
    URI: string;
    PreviewURL: string;
    Width: number;
    Height: number;
  }> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = async () => {
        try {
          const base64String = reader.result as string;
          const response = await fetch(
            "https://capcut-web-sg.bytedance.net/lv/marketing/pic/agent_core/conversation/upload_file",
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                BinaryBase64: base64String.split(",")[1],
              }),
            }
          );
          if (response.ok) {
            const result = await response.json();
            resolve(result);
          } else {
            reject(`图片上传失败: ${response.statusText}`);
          }
        } catch (e) {
          console.error(e);
          reject("图片上传失败");
        }
      };
      reader.onerror = (error) => {
        console.error(error);
        reject("图片文件读取失败");
      };
      reader.readAsDataURL(file);
    });
  }
}
