"use client";

import { useEffect, useState } from "react";
import React from "react";
import { SettingButton } from "./components/setting-button";
import { HistoryListButton } from "./components/history-list-button";
import { NewConversationButton } from "./components/new-conversation-button";

import {
  NuroConversationManager,
  NuroConversationState,
  NuroFile,
  NuroFileType,
  NuroLocalFile,
  NuroMCPManager,
  NuroMessage,
  NuroTask,
  NuroUserMessage,
  NuroUtils,
  SSETransport,
  RefContent,
  NuroSetting,
  NuroToolCallMessage
} from "@byted/nurosdk-js";
import { installEnv, installPlugins } from "./install";
import { NuroCellBuilder } from "./nuro-components/builder";
import { hostAgentEndpoint } from "./config";
import { ShareButton } from "./components/share-button";
import Image from "next/image";
import {
  initializeTransportOptions,
} from "./utils/llmOptionsHelper";
import { loadAndProcessConversationHistory } from "./utils/conversationHistoryHelper";
import { PluginCenter } from "./plugin-system/plugin-center";
import { NuroTaskCellBuilder } from "./nuro-components/task-cell-builder";
import EventStreamLogViewer from "./components/event-stream-log-viewer";
import MainLayout from "../components/main-layout";
import { setLatestLogIdAtom, associateMessageLogIdAtom } from "./store/messageLogStore";
import { useSetAtom } from 'jotai';

function _Page() {
  const [conversationManager] = useState(new NuroConversationManager());
  const [nuroMessages, setNuroMessages] = useState<NuroMessage[]>([]);
  const [nuroTasks, setNuroTasks] = useState<NuroTask[]>([]);
  const [isStreaming, setIsStreaming] = useState(false);
  const [selectedImages, setSelectedImages] = useState<(File | string)[]>([]);
  const [imagePreviews, setImagePreviews] = useState<string[]>([]);
  const [debugInfo, setDebugInfo] = useState({ uid: "", did: "", aid: "" }); // 用于存储调试信息的状态变量
  const [inputText, setInputText] = useState(""); // 新增：用于管理输入框文本的状态
  const [refArray, setRefContent] = useState<RefContent[] | null>(null); // 新增：用于存储引用文本的状态
  const [summaryData, setSummaryData] = useState<string | null>(null);
  const [isCollapsed, setIsCollapsed] = useState(!!summaryData); // 新增：用于存储summary的状态

  const setLatestLogId = useSetAtom(setLatestLogIdAtom);
  const associateMessageLogId = useSetAtom(associateMessageLogIdAtom);

  useEffect(() => {
    (async () => {
      installEnv();
      installPlugins();

      // 拦截 fetch API 以实现请求-响应绑定
      const originalFetch = window.fetch;
      window.fetch = async (...args) => {
        const [url, options] = args;
        const requestUrl = typeof url === 'string' ? url : url.toString();
        
        // 为每个 chat_proxy 请求注册待处理状态
        let messageId: string = '';
        const response = await originalFetch(...args);
        
        // 缓存最新的logId（不立即关联到具体消息）
        if (requestUrl.includes('/api/chat_proxy')) {
          const logIdFromHeader = response.headers.get('x-tt-logid');
          if (logIdFromHeader) {
            setLatestLogId(logIdFromHeader);
          }
        }
        
        return response;
      };

      const transportOptions = await initializeTransportOptions();

      try {
        const userInfoResponse = await fetch(hostAgentEndpoint + "/user_info", {
          credentials: "include",
        });
        setDebugInfo(await userInfoResponse.json());
      } catch (error) {}

      console.log("transportOptions", transportOptions);

      let transport: SSETransport;
      if (transportOptions["x-hostagent-url"]) {
        let forceHostAgentEndpoint = transportOptions["x-hostagent-url"];
        let forceUseProxy = transportOptions["x-hostagent-useproxy"];
        if (forceUseProxy === "true") {
          transportOptions["x-url"] = forceHostAgentEndpoint;
          transport = new SSETransport("/api/chat_proxy", transportOptions);
        } else {
          transport = new SSETransport(
            forceHostAgentEndpoint,
            transportOptions
          );
        }
      } else {
        transport = new SSETransport(
          hostAgentEndpoint + "/sse",
          transportOptions
        );
      }

      const mcpManager = new NuroMCPManager();
      PluginCenter.instance.getMCPServers().forEach((it) => {
        mcpManager.registerServer(it);
      });
      conversationManager.mcpManager = mcpManager;
      conversationManager.enableMCPTools();

      conversationManager.connect(transport);

      conversationManager.conversation.taskChecker =
        PluginCenter.instance.getTaskChecker();

      conversationManager.conversation.addStateUpdateListener((state) => {
        if (
          state === NuroConversationState.preparing ||
          state === NuroConversationState.streamingResponse
        ) {
          setIsStreaming(true);
        } else {
          setIsStreaming(false);
        }
        console.log(`Conversation state updated: ${state}`);
      });
      conversationManager.conversation.addMessageUpdateListener((message,op) => {
        console.log(`Message updated: `, message,",op:",op);
        
        // 当消息状态变为 finished_successfully 时，关联logId
        const isFinished = message.isFinalStatus();
        if (isFinished && message.id) {
          console.log(`Associating logId with message: ${message.id}`);
          associateMessageLogId(message.id);
        }
        
        setNuroMessages([...conversationManager.conversation.messages]);
      });
      conversationManager.conversation.addSystemDataListener((systemData) => {
        if (systemData.type === 'summary') {
          setSummaryData(systemData.content ?? '');
        }
      })


      conversationManager.conversation.addTaskUpdateListener((taskOuts) => {
        taskOuts.forEach((taskOut) => {
          console.log(`Task updated: `, taskOut.task,`，op`,taskOut.taskOp,`，msgs`,taskOut.messages[0]?.message,`，msgops`,taskOut.messages[0]?.messageOp);
        })

        setNuroTasks([...conversationManager.conversation.tasks]);
      });

      (async () => {
        const urlConversationId = new URL(
          window.location.href
        ).searchParams.get("conversation_id");
        const urlUid = new URL(window.location.href).searchParams.get("uid");
        if (
          typeof urlConversationId === "string" &&
          urlConversationId.length > 0
        ) {
          await loadAndProcessConversationHistory(
            conversationManager,
            urlConversationId,
            urlUid
          );
          setNuroMessages(conversationManager.conversation.messages);
          setNuroTasks(conversationManager.conversation.tasks);
        }
      })();
    })();
  }, [setLatestLogId, associateMessageLogId]);
    useEffect(() => {
    if (summaryData) {
      setIsCollapsed(true);
    }
  }, [summaryData]);

  const [isSettingsOpen, setIsSettingsOpen] = useState(false);

  const handleImageSelect = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      const mediaFiles = Array.from(files).filter((file) =>
        file.type.startsWith("image/") || file.type.startsWith("video/")
      );

      if (mediaFiles.length === 0) {
         event.target.value = ""; // Clear the input if no media files are selected
         return;
       }

      const newSelectedImages: File[] = [];
      const newImagePreviewPromises: Promise<string>[] = [];

      mediaFiles.forEach((file) => {
        if (file.type.startsWith("video/")) {
          const maxSize = 50 * 1024 * 1024; // 50MB in bytes
          if (file.size > maxSize) {
            alert(`Video file ${file.name} exceeds the 50MB size limit and will not be uploaded.`);
            return; // Skip this file
          }
        }
        newSelectedImages.push(file);
                if (file.type.startsWith("image/")) {
          newImagePreviewPromises.push(
            new Promise((resolve, reject) => {
              const reader = new FileReader();
              reader.onloadend = () => {
                resolve(reader.result as string);
              };
              reader.onerror = reject;
              reader.readAsDataURL(file);
            })
          );
        } else if (file.type.startsWith("video/")) {
          // For videos, we can use a generic icon or a placeholder, as generating video previews can be complex.
          // Alternatively, you could use the file name or a generic video icon URL.
          newImagePreviewPromises.push(
            new Promise((resolve) => {
              const video = document.createElement('video');
              video.src = URL.createObjectURL(file);
              video.onloadeddata = () => {
                video.currentTime = 0; // Seek to the first frame
              };
              video.onseeked = () => {
                const canvas = document.createElement('canvas');
                canvas.width = video.videoWidth;
                canvas.height = video.videoHeight;
                const ctx = canvas.getContext('2d');
                if (ctx) {
                  ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
                  resolve(canvas.toDataURL());
                  URL.revokeObjectURL(video.src); // Clean up object URL
                } else {
                  resolve("/file.svg"); // Fallback if canvas context is not available
                  URL.revokeObjectURL(video.src);
                }
              };
              video.onerror = () => {
                resolve("/file.svg"); // Fallback on video load error
                URL.revokeObjectURL(video.src);
              };
            })
          );
        }
      });

      try {
        const newImagePreviews = await Promise.all(newImagePreviewPromises);
        console.log("newSelectedImages", newSelectedImages);
        // Filter out any undefined previews that might result from skipped files
        const validNewImagePreviews = newImagePreviews.filter(p => p !== undefined) as string[];
        // Filter out files that were skipped due to size limit
        const validNewSelectedImages = newSelectedImages.filter((_, index) => newImagePreviewPromises[index] !== undefined);

        setSelectedImages((prev) => [...prev, ...validNewSelectedImages]);
        setImagePreviews((prev) => [...prev, ...validNewImagePreviews]);
      } catch (error) {
        console.error("Error reading image files:", error);
        // Optionally, provide user feedback about the error
      }

      // Clear the input value to allow selecting the same file again if needed
      event.target.value = "";
    }
  };

  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault?.();
    const text = inputText.trim(); // 修改：使用 inputText state

    if (!text && selectedImages.length === 0) {
      return; // 如果都为空，则不执行任何操作
    }

    const files: NuroFile[] = [];
    if (selectedImages.length > 0) {
      selectedImages.forEach((image) => {
          if (typeof image === "string") {
            // This case handles URLs, assuming they are images by default for now.
            // A more robust solution might involve checking file extensions or MIME types from the URL if possible.
            files.push(new NuroFile(NuroFileType.image, image));
          } else {
            // Handle File objects
            if (image.type.startsWith("image/")) {
              files.push(
                new NuroFile(
                  NuroFileType.image,
                  undefined,
                  undefined,
                  new NuroLocalFile(image.name, image)
                )
              );
            } else if (image.type.startsWith("video/")) {
              files.push(
                new NuroFile(
                  NuroFileType.video, // Ensure NuroFileType.video is defined in NuroSDK
                  undefined,
                  undefined,
                  new NuroLocalFile(image.name, image)
                )
              );
            }
          }
        });
    }


    const userMessage = new NuroUserMessage(
      NuroUtils.randomUUIDString(),
      text,
      files.length > 0 ? files : undefined,
      undefined,
      refArray && refArray.length > 0 ? refArray : undefined,
    );
    conversationManager.sendUserMessage(userMessage);
    // (document.getElementById("user_input") as HTMLInputElement).value = "";
    setInputText(""); // 修改：清空 inputText state
    setRefContent(null);
    setImagePreviews([]);
    setSelectedImages([]);
  };

  const handleRetry = (message: NuroMessage) => {
    conversationManager.regenerateMessage(message, true);
  };

  const handleReference = (refContentArray: RefContent[]) => {
    if (refArray && refArray.length > 0) {
      setRefContent([...refArray, ...refContentArray]);
    } else {
      setRefContent(refContentArray);
    }
  }

  return (
      <MainLayout>
        <div className="py-8">
        <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <div
          style={{
            display:
              conversationManager.conversation.messages.length > 0
                ? "none"
                : "flex",
            justifyContent: "center",
            marginTop: "45%",
            paddingBottom: 22,
          }}
        >
          <Image
            src="/logo.png"
            alt="Logo"
            width={640 * 0.5}
            height={171 * 0.5}
          />
        </div>
        <div className="flex justify-between items-center mb-4">
          <HistoryListButton />
          {conversationManager.conversation.conversationId && (
            <NewConversationButton />
          )}
          <SettingButton onSettingShowed={(v) => setIsSettingsOpen(v)} />
          {conversationManager.conversation.conversationId && (
            <ShareButton
              conversationId={conversationManager.conversation.conversationId}
              uid={debugInfo.uid}
            />
          )}
        </div>
        <div style={{ height: 44 }}></div>
        <div
          className="space-y-6 mb-6 flex flex-col"
          style={{ visibility: isSettingsOpen ? "hidden" : "unset" }}
        >
          {summaryData && (
            <div>
              <div className="text-right mb-4">
                <button
                  onClick={() => setIsCollapsed(!isCollapsed)}
                  className="text-blue-500 hover:underline"
                >
                  {isCollapsed ? '展开全部消息' : '折叠消息'}
                </button>
              </div>
              {isCollapsed && (
                <div className="p-4 bg-gray-50 rounded-lg border border-gray-200 mb-4">
                  <h3 className="font-semibold text-gray-800 mb-2">Summary</h3>
                  <div className="text-gray-700">{summaryData}</div>
                </div>
              )}
            </div>
          )}
          {nuroTasks.length <= 0 ? nuroMessages
            .filter(message => !isCollapsed || message instanceof NuroToolCallMessage)
            .map((message) => (
              <NuroCellBuilder
                key={message.id}
                message={message}
                onRetry={handleRetry}
                hideTextContent={isCollapsed}
                onArgosClick={(logId) => window.open(`https://argos.bytedance.com/chat?logId=${logId}`, '_blank')}
                onFornaxClick={(logId) => window.open(`https://fornax.bytedance.com/chat?logId=${logId}`, '_blank')}
              />
            )): []}
          {nuroTasks.map((task) => (
            <NuroTaskCellBuilder
              key={task.taskId}
              task={task}
              onRetry={handleRetry}
              onReference = {handleReference}
            />
          ))}
          {isStreaming && (
            <div className="p-6 rounded-2xl bg-white shadow-sm border border-gray-100">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-blue-500 animate-pulse"></div>
                <div className="w-2 h-2 rounded-full bg-blue-500 animate-pulse delay-100"></div>
                <div className="w-2 h-2 rounded-full bg-blue-500 animate-pulse delay-200"></div>
              </div>
            </div>
          )}
          <div style={{ height: 44 }}></div>
        </div>
        </div>
        <form
          onSubmit={handleFormSubmit}
          className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 bottom-8 bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200 p-4"
          style={
            conversationManager.conversation.messages.length > 0
              ? {
                  // 使用 fixed 定位使元素悬浮在底部
                  position: "fixed",
                  // 距离底部 20px
                  bottom: 20,
                  // 水平居中
                  left: "50%",
                  // 结合 left 和 transform 实现水平居中
                  transform: "translateX(-50%)",
                  // 设置宽度，使其在小屏幕上自适应，大屏幕上最大700px
                  width: "calc(100% - 2rem)", // 左右各留1rem边距
                  maxWidth: 700,
                  // 根据设置面板是否打开控制可见性
                  visibility: isSettingsOpen ? "hidden" : "unset",
                }
              : {
                  visibility: isSettingsOpen ? "hidden" : "unset",
                }
          }
        >
          {refArray && refArray.length > 0 && (
            <div className="mb-2 p-2 bg-gray-100 rounded-lg text-sm text-gray-700 flex flex-wrap gap-2 items-center">
              <div >引用：</div>
              {refArray.map((refItem, index) => {
                if (refItem.file && refItem.file.type === 'image') { // Assuming NuroFileType.image is 'image'
                  return (
                    <div key={index} className="relative w-16 h-16 group">
                      <img
                        src={refItem.file.url} // Assuming URL is in refItem.file.url
                        alt={`reference-image-${index}`}
                        className="w-full h-full object-cover rounded-md"
                      />
                      <button
                        type="button"
                        onClick={(e) => {
                          e.stopPropagation();
                          const newRefArray = refArray.filter((_, i) => i !== index);
                          setRefContent(newRefArray.length > 0 ? newRefArray : null);
                        }}
                        className="absolute top-0 right-0 m-1 p-0.5 bg-black/50 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200 text-xs"
                        aria-label={`删除引用 ${index}`}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-3 h-3">
                          <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </div>
                  );
                } else if (refItem.text) {
                  return (
                    <div key={index} className="relative flex-grow min-w-0 group">
                      <p 
                        className="overflow-hidden text-ellipsis whitespace-nowrap p-1 bg-white rounded border border-gray-300"
                        title={refItem.text} // Show full text on hover
                      >
                        {refItem.text}
                      </p>
                      <button
                        type="button"
                        onClick={(e) => {
                          e.stopPropagation();
                          const newRefArray = refArray.filter((_, i) => i !== index);
                          setRefContent(newRefArray.length > 0 ? newRefArray : null);
                        }}
                        className="absolute top-0 right-0 m-0.5 p-0.5 bg-black/50 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200 text-xs"
                        aria-label={`删除引用 ${index}`}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-3 h-3">
                          <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </div>
                  );
                }
                return null;
              })}
              {/* Optional: Button to clear all references */}
              {refArray.length > 0 && (
                 <button 
                    type="button" 
                    onClick={(e) => { e.stopPropagation(); setRefContent(null); }} 
                    className="ml-auto text-xs text-gray-500 hover:text-gray-700 p-1 bg-gray-200 rounded-full"
                    title="清除所有引用"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-3 h-3">
                       <path strokeLinecap="round" strokeLinejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12.56 0c1.153 0 2.24.032 3.223.094M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </button>
              )}
            </div>
          )}
          <div className="flex gap-3 items-center">
            <div className="relative">
              <input
                type="file"
                accept="image/*,video/*"
                multiple // Allow multiple file selection
                onChange={handleImageSelect}
                className="hidden"
                id="image-upload"
                disabled={isStreaming}
              />
              <label
                htmlFor="image-upload"
                className="cursor-pointer p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 flex items-center justify-center mt-1"
              >
                <svg
                  className="w-6 h-6 text-gray-600"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                  />
                </svg>
              </label>
            </div>
            {imagePreviews.length > 0 &&
              imagePreviews.map((previewUrl, index) => (
                <div key={index} className="relative w-12 h-12">
                  <img
                    src={previewUrl}
                    alt={`Preview ${index}`}
                    className="w-full h-full object-cover rounded-lg"
                  />
                  <div
                    onClick={() => {
                      const newSelectedImages = [...selectedImages];
                      const newImagePreviews = [...imagePreviews];
                      newSelectedImages.splice(index, 1);
                      newImagePreviews.splice(index, 1);
                      setSelectedImages(newSelectedImages);
                      setImagePreviews(newImagePreviews);
                      // No need to reset fileInput.value here as we clear it in handleImageSelect
                    }}
                    tabIndex={-1}
                    className="absolute -top-1 -right-1 bg-black/60 text-white rounded-lg w-6 h-6 flex items-center justify-center hover:bg-black/80 transition-colors duration-200 cursor-pointer"
                    aria-label={`删除图片 ${index}`}
                  >
                    <svg
                      className="w-4 h-4"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </div>
                </div>
              ))}
            <input
              id="user_input"
              placeholder={`请输入你的想法...(${NuroSetting.version})`}
              disabled={isStreaming}
              value={inputText} // 新增：使输入框受控
              onChange={(e) => setInputText(e.target.value)} // 新增：更新 inputText state
              style={{
                maxWidth: isStreaming
                  ? "calc(100% - 220px)"
                  : "calc(100% - 140px)",
              }}
              className="w-[800px] px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:opacity-50"
            />
            <button
              id="global_submit_button"
              type="submit"
              disabled={
                isStreaming ||
                (!inputText.trim() && selectedImages.length === 0)
              } // 修改：更新禁用条件
              onContextMenu={(e) => {
                e.preventDefault();
                alert(JSON.stringify(debugInfo));
              }}
              className="px-6 py-3 bg-blue-500 text-white rounded-xl font-medium hover:opacity-90 disabled:opacity-50 transition-all duration-200 shadow-sm hover:shadow flex-shrink-0 flex items-center justify-center"
            >
              {!isStreaming ? (
                "发送"
              ) : (
                <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-white"></div>
              )}
            </button>
            {isStreaming && (
              <button
                onClick={() => {
                  conversationManager.interruptResponse();
                }}
                className="px-6 py-3 bg-red-500 text-white rounded-xl font-medium hover:opacity-90 transition-all duration-200 shadow-sm hover:shadow flex-shrink-0 flex items-center justify-center"
              >
                停止
              </button>
            )}
          </div>
        </form>
      </div>
    </MainLayout>
  );
}

import { ChatProviders } from "./providers";

export default function Page() {
  return (
    <ChatProviders>
      <_Page />
    </ChatProviders>
  );
}
