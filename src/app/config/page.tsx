"use client";

import { Suspense } from "react";
import MainLayout from "../components/main-layout";
import { useSearchParams } from "next/navigation";

function ConfigPage() {
  const searchParams = useSearchParams();
  const tab = searchParams.get("tab");

  const isPromptTab = tab === "prompt";
  const iframeSrc =
    tab === "agent"
      ? "https://cloud.bytedance.net/tcc/namespace/videocut.dreamina.agent_core?by_key=false&condition=name&configId=&dir_path=all_dir&env=ppe_smart_video&filter_empty_dir=false&filter_no_tag=false&keyword=&order=&pn=1&region=all_region&release_operator=&release_status=&rn=20&scope=all&source=&tab=&x-resource-account=public&x-bc-region-id=bytedance"
      : "https://fornax.bytedance.net/space/7527962177773240321/prompt/develop/7530534276182310934";

  let style = {};
  if (isPromptTab) {
    style = {
      border: "none",
      position: "absolute",
      left: "-240px", // 减少左侧隐藏，避免遮挡内容
      width: "calc(100vw + 240px)", // 调整宽度补偿
      height: "calc(100vh + 80px)", // 使用视窗高度
      minWidth: "1400px", // 确保最小宽度
    };
  } else {
    style = {
      border: "none",
      position: "absolute",
      width: "calc(100vw + 240px)", // 调整宽度补偿
      height: "calc(100vh + 80px)", // 使用视窗高度
      minWidth: "1400px", // 确保最小宽度
    };
  }
  return (
    <MainLayout>
      <div className="relative overflow-auto" style={{ height: "100vh" }}>
        <iframe
          src={iframeSrc}
          style={style}
          title="链路追踪监控"
          allow="fullscreen"
        />
      </div>
    </MainLayout>
  );
}

export default function () {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ConfigPage />
    </Suspense>
  );
}
