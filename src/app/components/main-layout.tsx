"use client";

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

interface MainLayoutProps {
  children: React.ReactNode;
}

export default function MainLayout({ children }: MainLayoutProps) {
  const pathname = usePathname();
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [openDropdown, setOpenDropdown] = useState<string | null>(null);
  
  const navItems = [
    { href: '/chat', label: '对话', icon: '💬' },
    // { href: '/agents', label: 'Agent管理', icon: '🤖' },
    { 
      href: '/evaluation', 
      label: '评测', 
      icon: '📊',
      external: true,
      externalUrl: 'https://agihub.bytedance.net/delivery_center/2917685762/2949439490/byteval/project/2917686762/manageWorkflow?paramsId=1576221'
    },
    { href: '/debug', label: '调试', icon: '🔧' },
    { href: '/history', label: '历史', icon: '📝' },
    { 
      href: '/config', 
      label: '配置', 
      icon: '🔧',
      subItems: [
        { href: '/config?tab=agent', label: 'Agent 配置' },
        { href: '/config?tab=prompt', label: 'Prompt 配置' }
      ]
    },
  ];

  const handleNavClick = (e: React.MouseEvent, item: any) => {
    // 如果有子菜单项，则切换下拉菜单的显示状态
    if (item.subItems) {
      e.preventDefault();
      setOpenDropdown(openDropdown === item.href ? null : item.href);
      return;
    }
    
    if (item.external && item.externalUrl) {
      e.preventDefault();
      window.open(item.externalUrl, '_blank', 'noopener,noreferrer');
    }
    // 移动端点击后关闭菜单
    if (window.innerWidth < 768) {
      setMobileMenuOpen(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white">
      {/* 移动端遮罩层 */}
      {mobileMenuOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-30 z-40 md:hidden"
          onClick={() => setMobileMenuOpen(false)}
        />
      )}

      {/* 移动端顶部栏 */}
      <div className="md:hidden bg-white shadow-sm border-b border-gray-200 px-4 py-3 flex items-center justify-between">
        <Link href="/" className="flex items-center space-x-2">
          <span className="text-xl font-bold text-blue-600">Nuro Agent</span>
        </Link>
        
        <button
          onClick={() => setMobileMenuOpen(true)}
          className="flex items-center justify-center w-8 h-8 rounded-lg hover:bg-gray-100 transition-colors"
        >
          <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </button>
      </div>

      <div className="flex min-h-screen md:min-h-full">
        {/* 移动端侧边栏 */}
        <div className={`
          fixed left-0 top-0 h-full bg-white shadow-lg border-r border-gray-200 z-50 transition-all duration-300 ease-in-out w-64 md:hidden
          ${mobileMenuOpen ? 'translate-x-0' : '-translate-x-full'}
        `}>
          {/* 移动端侧边栏头部 */}
          <div className="flex items-center justify-between h-16 px-4 border-b border-gray-200">
            <Link href="/" className="flex items-center space-x-2">
              <span className="text-xl font-bold text-blue-600">Nuro Agent</span>
            </Link>
            
            <button
              onClick={() => setMobileMenuOpen(false)}
              className="flex items-center justify-center w-8 h-8 rounded-lg hover:bg-gray-100 transition-colors"
            >
              <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* 移动端导航菜单 */}
          <nav className="flex-1 px-2 py-4 space-y-1">
            {navItems.map((item) => (
              <div key={item.href}>
                <Link
                  href={item.href}
                  onClick={(e) => handleNavClick(e, item)}
                  className={
                    `flex items-center px-3 py-3 text-sm font-medium rounded-lg transition-all duration-200 justify-between ${item.subItems ? 'cursor-pointer' : ''} ` +
                    (pathname === item.href && !item.subItems
                      ? 'text-blue-600 bg-blue-50 border-r-2 border-blue-600'
                      : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50')
                  }
                >
                  <div className="flex items-center">
                    <span className="text-lg flex-shrink-0">{item.icon}</span>
                    <span className="ml-3 flex-1">{item.label}</span>
                  </div>
                  {item.subItems && (
                    <svg 
                      className={`w-4 h-4 transition-transform duration-200 ${openDropdown === item.href ? 'rotate-180' : ''}`}
                      fill="none" 
                      stroke="currentColor" 
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  )}
                  {item.external && !item.subItems && (
                    <svg className="w-3 h-3 ml-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                  )}
                </Link>
                
                {/* 移动端下拉菜单 */}
                {item.subItems && openDropdown === item.href && (
                  <div className="pl-8 pr-2 py-2 space-y-1 bg-gray-50 rounded-lg mt-1 ml-3 mr-3">
                    {item.subItems.map((subItem: any) => (
                      <Link
                        key={subItem.href}
                        href={subItem.href}
                        onClick={(e) => {
                          if (subItem.external) {
                            e.preventDefault();
                            window.open(subItem.href, '_blank', 'noopener,noreferrer');
                          }
                          setMobileMenuOpen(false);
                        }}
                        className={
                          `block px-3 py-2 text-sm rounded-lg transition-all duration-200 ` +
                          (pathname === subItem.href
                            ? 'text-blue-600 bg-blue-50'
                            : 'text-gray-700 hover:text-blue-600 hover:bg-gray-100')
                        }
                      >
                        <div className="flex items-center justify-between">
                          <span>{subItem.label}</span>
                          {subItem.external && (
                            <svg className="w-3 h-3 ml-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                            </svg>
                          )}
                        </div>
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </nav>
        </div>

        {/* 桌面端左侧边栏 */}
        <div className={`
          hidden md:flex md:flex-col bg-white shadow-lg border-r border-gray-200 transition-all duration-300 ease-in-out
          ${sidebarCollapsed ? 'w-16' : 'w-64'}
        `}>
          {/* 桌面端侧边栏头部 */}
          <div className="flex items-center justify-between h-16 px-4 border-b border-gray-200">
            {!sidebarCollapsed && (
              <Link href="/" className="flex items-center space-x-2">
                <span className="text-xl font-bold text-blue-600">Nuro Agent</span>
              </Link>
            )}
            
            {/* 展开收起按钮 */}
            <button
              onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
              className="flex items-center justify-center w-8 h-8 rounded-lg hover:bg-gray-100 transition-colors"
            >
              <svg 
                className={`w-5 h-5 text-gray-600 transition-transform duration-300 ${sidebarCollapsed ? 'rotate-180' : ''}`} 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
          </div>

          {/* 桌面端导航菜单 */}
          <nav className="flex-1 px-2 py-4 space-y-1">
            {navItems.map((item) => (
              <div key={item.href}>
                <Link
                  href={item.href}
                  onClick={(e) => handleNavClick(e, item)}
                  className={`group flex items-center px-3 py-3 text-sm font-medium rounded-lg transition-all duration-200 relative justify-between ${item.subItems ? 'cursor-pointer' : ''} ${pathname === item.href && !item.subItems ? 'text-blue-600 bg-blue-50 border-r-2 border-blue-600' : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'}`}
                  title={sidebarCollapsed ? item.label : undefined}
                >
                  <div className="flex items-center">
                    <span className="text-lg flex-shrink-0">{item.icon}</span>
                    
                    {!sidebarCollapsed && (
                      <>
                        <span className="ml-3 flex-1">{item.label}</span>
                      </>
                    )}

                    {/* 收起状态下的悬浮提示 */}
                    {sidebarCollapsed && (
                      <div className="absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-xs rounded-md opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
                        {item.label}
                      </div>
                    )}
                  </div>
                  
                  {!sidebarCollapsed && item.subItems && (
                    <svg 
                      className={`w-4 h-4 transition-transform duration-200 ${openDropdown === item.href ? 'rotate-180' : ''}`}
                      fill="none" 
                      stroke="currentColor" 
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  )}
                  
                  {!sidebarCollapsed && item.external && !item.subItems && (
                    <svg className="w-3 h-3 ml-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                  )}
                </Link>
                
                {/* 桌面端下拉菜单 */}
                {!sidebarCollapsed && item.subItems && openDropdown === item.href && (
                  <div className="pl-8 pr-2 py-2 space-y-1 bg-gray-50 rounded-lg mt-1 ml-3 mr-3">
                    {item.subItems.map((subItem: any) => (
                      <Link
                        key={subItem.href}
                        href={subItem.href}
                        onClick={(e) => {
                          if (subItem.external) {
                            e.preventDefault();
                            window.open(subItem.href, '_blank', 'noopener,noreferrer');
                          }
                        }}
                        className={
                          `block px-3 py-2 text-sm rounded-lg transition-all duration-200 ` +
                          (pathname === subItem.href
                            ? 'text-blue-600 bg-blue-50'
                            : 'text-gray-700 hover:text-blue-600 hover:bg-gray-100')
                        }
                      >
                        <div className="flex items-center justify-between">
                          <span>{subItem.label}</span>
                          {subItem.external && (
                            <svg className="w-3 h-3 ml-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                            </svg>
                          )}
                        </div>
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </nav>
        </div>

        {/* 右侧主内容区域 */}
        <div className="flex-1 flex flex-col min-h-screen">
          <main className="flex-1 overflow-auto">
            {children}
          </main>
        </div>
      </div>
    </div>
  );
}