"use client";

import Link from 'next/link';
import Image from 'next/image';
import MainLayout from './components/main-layout';

export default function HomePage() {
  const features = [
    // {
    //   title: 'Agent 管理',
    //   description: '创建、编辑和管理您的智能代理',
    //   href: '/agents',
    //   icon: '🤖',
    //   color: 'bg-blue-50 hover:bg-blue-100 border-blue-200'
    // },
    {
      title: '对话聊天',
      description: '与您的 Agent 进行智能对话',
      href: '/chat',
      icon: '💬',
      color: 'bg-green-50 hover:bg-green-100 border-green-200'
    },
    {
      title: 'Agent 评测',
      description: '测试和评估 Agent 的性能表现',
      href: '/evaluation',
      icon: '📊',
      color: 'bg-purple-50 hover:bg-purple-100 border-purple-200'
    },
    {
      title: '调试工具',
      description: '调试和优化您的 Agent',
      href: '/debug',
      icon: '🔧',
      color: 'bg-orange-50 hover:bg-orange-100 border-orange-200'
    },
    {
      title: '历史记录',
      description: '查看对话历史和操作记录',
      href: '/history',
      icon: '📝',
      color: 'bg-gray-50 hover:bg-gray-100 border-gray-200'
    },
    {
      title: '配置管理',
      description: '管理 Agent 的配置',
      href: '/config',
      icon: '🔧',
      color: 'bg-orange-50 hover:bg-orange-100 border-orange-200'
    }
  ];

  return (
    <MainLayout>
      <div className="py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Logo 和欢迎信息 */}
          <div className="text-center mb-12">
            <div className="flex justify-center mb-8">
              <Image
                src="/logo.png"
                alt="Logo"
                width={320}
                height={85}
              />
            </div>
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              欢迎使用 Nuro Agent 管理平台
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              一站式 AI Agent 管理、调试、评测平台，助您轻松构建和优化智能代理
            </p>
          </div>

          {/* 功能卡片网格 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
            {features.map((feature) => (
              <Link
                key={feature.href}
                href={feature.href}
                className={`block p-6 rounded-xl border-2 transition-all duration-200 hover:shadow-lg hover:scale-105 ${feature.color}`}
              >
                <div className="flex items-center mb-4">
                  <span className="text-3xl mr-3">{feature.icon}</span>
                  <h3 className="text-xl font-semibold text-gray-900">{feature.title}</h3>
                </div>
                <p className="text-gray-600">{feature.description}</p>
              </Link>
            ))}
          </div>
        </div>
      </div>
    </MainLayout>
  );
}