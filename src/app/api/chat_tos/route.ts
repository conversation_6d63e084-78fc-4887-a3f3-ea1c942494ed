import { NextRequest, NextResponse } from "next/server";
import { tosClient } from "../chat_share/tosService";

export async function POST(request: NextRequest) {
  const body = await request.json();
  const { conversation_id } = body;
  if (!conversation_id) {
    return NextResponse.json(
      { error: "conversationId is required" },
      { status: 400 }
    );
  }
  const tosRes = await tosClient.getObject(`share/${conversation_id}.json`);
  const tosText = tosRes.objectBuffer.toString("utf-8");
  const tosData = JSON.parse(tosText);
  const realRes = await fetch(tosData["x-hostagent-conversation-item-url"], {
    method: "POST",
    headers: {
      "content-type": "application/json",
      "cookie": tosData["x-hostagent-cookie"],
      ...tosData,
    },
    body: JSON.stringify(body),
  });
  const text = await realRes.text();
  // console.log("text", text)
  // const data = await response.json();
  return NextResponse.json(text, {
    status: realRes.status,
    headers: {
      "Content-Type": "application/json",
    },
  });
}
