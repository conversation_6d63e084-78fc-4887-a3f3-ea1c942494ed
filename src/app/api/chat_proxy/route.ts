import { NextRequest, NextResponse } from "next/server";
import { tosClient } from "../chat_share/tosService";

export async function POST(request: NextRequest) {
  try {
    const targetUrl = request.headers.get("x-url");
    let hostAgentCookie = request.headers.get("x-hostagent-cookie");
    const hostAgentConversationId = request.headers.get(
      "x-hostagent-conversation-id"
    );

    if (hostAgentConversationId) {
      try {
        const tosRes = await tosClient.getObject(
          `share/${hostAgentConversationId}.json`
        );
        const tosText = tosRes.objectBuffer.toString("utf-8");
        const tosData = JSON.parse(tosText);
        hostAgentCookie = tosData["x-hostagent-cookie"];
      } catch (error) {}
    }

    if (!targetUrl) {
      return NextResponse.json(
        { error: "Missing target URL in x-url header" },
        { status: 400 }
      );
    }

    // 转发请求，保持原有headers
    const headers = new Headers(request.headers);
    headers.delete("host"); // 删除host header以避免冲突
    if (hostAgentCookie) {
      headers.set("Cookie", hostAgentCookie);
    }
    const response = await fetch(targetUrl, {
      method: "POST",
      headers: headers,
      body: await request.text(),
    });

    const resHeaders = new Headers(response.headers);
    // 检查响应是否为流式数据
    const contentType = response.headers.get("content-type");
    const isStreamResponse = contentType?.includes("text/event-stream");

    if (isStreamResponse) {
      // 处理流式响应
      const originalStream = response.body;
      if (!originalStream) {
        return NextResponse.json(
          { error: "Stream body is null" },
          { status: 500 }
        );
      }
      const newStream = new ReadableStream({
        async start(controller) {
          const reader = originalStream.getReader();
          try {
            while (true) {
              const { done, value } = await reader.read();
              if (done) {
                break;
              }
              controller.enqueue(value);
            }
          } catch (error) {
            console.error("Error reading from original stream:", error);
            controller.error(error);
          } finally {
            controller.close();
            reader.releaseLock();
          }
        },
      });

      resHeaders.set("Content-Type", "text/event-stream");
      resHeaders.set("Cache-Control", "no-cache");
      resHeaders.set("Connection", "keep-alive");
      resHeaders.set("X-Bytefaas-Enable-Stream", "true");

      return new NextResponse(newStream, {
        headers: resHeaders,
      });
    } else {
      // 处理普通响应
      const text = await response.text();
      // console.log("text", text)
      // const data = await response.json();
      resHeaders.set("Content-Type", "application/json");
      return NextResponse.json(text, {
        status: response.status,
        headers: resHeaders,
      });
    }
  } catch (error) {
    console.error("Proxy request error:", error);
    return NextResponse.json(
      { error: "Failed to proxy request" },
      { status: 500 }
    );
  }
}
