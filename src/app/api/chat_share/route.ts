import { NextRequest, NextResponse } from "next/server";
import { tosClient } from "./tosService";

export async function POST(request: NextRequest) {
  const body = await request.json();
  const { conversationId } = body;
  if (!conversationId) {
    return NextResponse.json(
      { error: "conversationId is required" },
      { status: 400 }
    );
  }
  await tosClient.putObject(
    `share/${conversationId}.json`,
    Buffer.from(JSON.stringify(body))
  );
  return NextResponse.json({ success: true });
}
