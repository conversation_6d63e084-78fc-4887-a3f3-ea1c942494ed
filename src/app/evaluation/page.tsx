"use client";

import MainLayout from '../components/main-layout';
import { useEffect } from 'react';

export default function EvaluationPage() {
  const evaluationUrl = 'https://agihub.bytedance.net/delivery_center/2917685762/2949439490/byteval/project/2917686762/manageWorkflow?paramsId=1576221';

  const openEvaluation = () => {
    window.open(evaluationUrl, '_blank', 'noopener,noreferrer');
  };

  useEffect(() => {
    // 页面加载时自动打开评测页面
    openEvaluation();
  }, []);

  return (
    <MainLayout>
      <div className="py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            {/* 图标 */}
            <div className="mx-auto flex items-center justify-center h-24 w-24 rounded-full bg-blue-100 mb-6">
              <svg className="h-12 w-12 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
              </svg>
            </div>

            {/* 标题 */}
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              Agent 评测
            </h1>
            
            {/* 描述 */}
            <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
              评测功能已在新窗口中打开。如果页面没有自动打开，请点击下方按钮手动访问 ByteVal 评测系统。
            </p>

            {/* 按钮 */}
            <div className="space-y-4">
              <button
                onClick={openEvaluation}
                className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
              >
                <span>打开 ByteVal 评测系统</span>
                <svg className="ml-2 -mr-1 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
              </button>
              
              <div className="text-sm text-gray-500">
                <p>或者直接访问：</p>
                <a 
                  href={evaluationUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:text-blue-800 underline break-all"
                >
                  {evaluationUrl}
                </a>
              </div>
            </div>

            {/* 功能说明 */}
            <div className="mt-12 bg-gray-50 rounded-lg p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">评测功能说明</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <span className="text-blue-600">📊</span>
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">性能评测</h3>
                    <p className="text-gray-600 text-sm">测试和评估 Agent 的性能表现</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <span className="text-green-600">🎯</span>
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">准确性分析</h3>
                    <p className="text-gray-600 text-sm">分析 Agent 回答的准确性和质量</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <span className="text-purple-600">⚡</span>
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">响应速度</h3>
                    <p className="text-gray-600 text-sm">监控 Agent 的响应时间和效率</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <span className="text-orange-600">📈</span>
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">数据报告</h3>
                    <p className="text-gray-600 text-sm">生成详细的评测报告和分析</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}