import { Client } from "@modelcontextprotocol/sdk/client/index.js";
import { InMemoryTransport } from "@modelcontextprotocol/sdk/inMemory.js";
import { Tool } from "@modelcontextprotocol/sdk/types.js";
import OpenAI from "openai";

type OpenAIChatCompletionTool = OpenAI.ChatCompletionTool;

export class MCPDeviceServerManager_HostSide {
  constructor(readonly clientTools: { name: string; tools: Tool[] }[]) {}

  hasClientTool(toolName: string): boolean {
    return this.clientTools.some(({ name, tools }) => tools.some((tool) => `${name}_${tool.name}` === toolName));
  }

  getAllToolsAsOpenAITools(): OpenAIChatCompletionTool[] {
    let openAITools: OpenAIChatCompletionTool[] = [];
    this.clientTools.forEach(({ name, tools }) => {
      tools.forEach((tool) => {
        openAITools.push({
          type: "function",
          function: {
            name: `${name}_${tool.name}`,
            description: tool.description,
            parameters: tool.inputSchema,
          },
        });
      });
    });
    return openAITools;
  }
}

export interface MCPDeviceServerConfig {
  name: string;
  clientTransport: InMemoryTransport;
}

interface ServerConnection {
  config: MCPDeviceServerConfig;
  client: Client;
  transport: InMemoryTransport;
  tools: Tool[];
  isConnected: boolean;
}

export class MCPDeviceServerManager_DeviceSide {
  private servers: Map<string, ServerConnection> = new Map();
  private toolsMapping: Map<string, { serverName: string; functionName: string }> = new Map();

  async registerServer(config: MCPDeviceServerConfig): Promise<void> {
    if (this.servers.has(config.name)) {
      throw new Error(`Server with id ${config.name} already exists`);
    }

    const transport = config.clientTransport;
    const client = new Client(
      {
        name: config.name,
        version: "1.0.0",
      },
      {
        capabilities: {
          prompts: {},
          resources: {},
          tools: {},
        },
      }
    );

    this.servers.set(config.name, {
      config,
      client,
      transport,
      tools: [],
      isConnected: false,
    });
  }

  async connectServer(serverName: string): Promise<void> {
    const server = this.servers.get(serverName);
    if (!server) {
      throw new Error(`Server ${serverName} not found`);
    }

    if (!server.isConnected) {
      await server.transport.start();
      await server.client.connect(server.transport);
      const toolsResult = await server.client.listTools();
      server.tools = toolsResult.tools;
      toolsResult.tools.forEach((tool) => {
        this.toolsMapping.set(`${serverName}_${tool.name}`, {
          serverName,
          functionName: tool.name,
        });
      });
      server.isConnected = true;
    }
  }

  async connectAll(): Promise<void> {
    const promises = Array.from(this.servers.keys()).map((serverName) => this.connectServer(serverName));
    await Promise.all(promises);
  }

  getServerTools(name: string): Tool[] {
    const server = this.servers.get(name);
    if (!server) {
      throw new Error(`Server ${name} not found`);
    }
    return server.tools;
  }

  getAllTools(): { name: string; tools: Tool[] }[] {
    return Array.from(this.servers.entries()).map(([name, server]) => ({
      name,
      tools: server.tools,
    }));
  }

  getAllToolsAsOpenAITools(): OpenAIChatCompletionTool[] {
    let openAITools: OpenAIChatCompletionTool[] = [];
    this.getAllTools().forEach(({ name, tools }) => {
      tools.forEach((tool) => {
        openAITools.push({
          type: "function",
          function: {
            name: `${name}_${tool.name}`,
            description: tool.description,
            parameters: tool.inputSchema,
          },
        });
      });
    });
    return openAITools;
  }

  async callTool(option: {
    functionNameWithPrefix: string;
    functionCallArguments?: any;
    functionCallId: string;
    extraArguments?: any;
    progressCallback?: (progress: string) => void;
  }): Promise<string> {
    const mapping = this.toolsMapping.get(option.functionNameWithPrefix);
    if (!mapping) {
      throw new Error(`Tool ${option.functionNameWithPrefix} not found`);
    }

    const server = this.servers.get(mapping.serverName);
    if (!server) {
      throw new Error(`Server ${mapping.serverName} not found`);
    }

    if (!server.isConnected) {
      throw new Error(`Server ${mapping.serverName} is not connected`);
    }

    let done = false;

    if (option.progressCallback) {
      const checkProgressTask = async () => {
        const progressFunctionName = `${mapping.serverName}_function_progress`;
        const progressResult = await this.callTool({
          functionNameWithPrefix: progressFunctionName,
          functionCallId: option.functionCallId,
        });
        if (progressResult) {
          option.progressCallback?.(progressResult);
        }
      };
      (async () => {
        while (!done) {
          await new Promise((resolve) => setTimeout(resolve, 5000));
          checkProgressTask();
        }
      })();
    }

    const result = await server.client.callTool({
      name: mapping.functionName,
      arguments: {
        ...(option.functionCallArguments ?? {}),
        $functionCallId: option.functionCallId,
        ...(option.extraArguments ?? {}),
      },
    });
    done = true;

    if (typeof result.content === "object" && result.content instanceof Array) {
      let text = "";
      result.content.forEach((it) => {
        if (it.type === "text") {
          text += it.text + "\n";
        } else {
          text += JSON.stringify(it);
        }
      });
      return text;
    } else {
      return `${JSON.stringify(result.content)}`;
    }
  }
}
