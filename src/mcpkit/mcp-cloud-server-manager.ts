import { Client } from "@modelcontextprotocol/sdk/client/index.js";
import { SSEClientTransport } from "@modelcontextprotocol/sdk/client/sse.js";
import { Tool } from "@modelcontextprotocol/sdk/types.js";
import OpenA<PERSON> from "openai";
import { Tool as aiTool } from "ai";

export interface MCPCloudServerConfig {
  name: string;
  url: string;
}

type OpenAIChatCompletionTool = OpenAI.ChatCompletionTool;

interface ServerConnection {
  config: MCPCloudServerConfig;
  client: Client;
  transport: SSEClientTransport;
  tools: Tool[];
  isConnected: boolean;
}

export class MCPCloudServerManager {
  private servers: Map<string, ServerConnection> = new Map();
  private toolsMapping: Map<string, { serverName: string; functionName: string }> = new Map();

  async registerServer(config: MCPCloudServerConfig): Promise<void> {
    if (this.servers.has(config.name)) {
      throw new Error(`Server with id ${config.name} already exists`);
    }

    const transport = new SSEClientTransport(new URL(config.url), {});
    const client = new Client(
      {
        name: config.name,
        version: "1.0.0",
      },
      {
        capabilities: {
          prompts: {},
          resources: {},
          tools: {},
        },
      }
    );

    this.servers.set(config.name, {
      config,
      client,
      transport,
      tools: [],
      isConnected: false,
    });
  }

  async connectServer(serverName: string): Promise<void> {
    const server = this.servers.get(serverName);
    if (!server) {
      throw new Error(`Server ${serverName} not found`);
    }

    if (!server.isConnected) {
      server.client.onclose = () => {
        console.log(`Server ${serverName} disconnected`);
        server.isConnected = false;
      };
      await server.client.connect(server.transport);
      const toolsResult = await server.client.listTools();
      server.tools = toolsResult.tools;
      toolsResult.tools.forEach((tool) => {
        this.toolsMapping.set(`${serverName}_${tool.name}`, {
          serverName,
          functionName: tool.name,
        });
      });
      server.isConnected = true;
    }
  }

  async connectAll(): Promise<void> {
    const promises = Array.from(this.servers.keys()).map((serverName) => this.connectServer(serverName));
    await Promise.all(promises);
  }

  getServerTools(name: string): Tool[] {
    const server = this.servers.get(name);
    if (!server) {
      throw new Error(`Server ${name} not found`);
    }
    return server.tools;
  }

  getAllTools(): { name: string; tools: Tool[] }[] {
    return Array.from(this.servers.entries()).map(([name, server]) => ({
      name,
      tools: server.tools,
    }));
  }

  getAllToolsAsOpenAITools(): OpenAIChatCompletionTool[] {
    let openAITools: OpenAIChatCompletionTool[] = [];
    this.getAllTools().forEach(({ name, tools }) => {
      tools.forEach((tool) => {
        openAITools.push({
          type: "function",
          function: {
            name: `${name}_${tool.name}`,
            description: tool.description,
            parameters: tool.inputSchema,
          },
        });
      });
    });
    return openAITools;
  }

  getAllToolsAsAITools(): Record<string, aiTool> {
    let aiTools: Record<string, aiTool> = {};
    this.getAllTools().forEach(({ name, tools }) => {
      tools.forEach((tool) => {
        const toolName = `${name}_${tool.name}`;
        aiTools[toolName] = {
          type: "function",
          description: tool.description,
          parameters: [],
          execute: async (args, options) => {
            this.callTool({
              functionNameWithPrefix: `${name}_${tool.name}`,
              functionCallArguments: args,
              functionCallId: options.toolCallId,
            });
          },
        };
      });
    });
    return aiTools;
  }

  async callTool(option: {
    functionNameWithPrefix: string;
    functionCallArguments?: any;
    functionCallId: string;
    extraArguments?: any;
    progressCallback?: (progress: string) => void;
  }): Promise<string> {
    const mapping = this.toolsMapping.get(option.functionNameWithPrefix);
    if (!mapping) {
      throw new Error(`Tool ${option.functionNameWithPrefix} not found`);
    }

    const server = this.servers.get(mapping.serverName);
    if (!server) {
      throw new Error(`Server ${mapping.serverName} not found`);
    }

    if (!server.isConnected) {
      throw new Error(`Server ${mapping.serverName} is not connected`);
    }

    let done = false;

    if (option.progressCallback) {
      const checkProgressTask = async () => {
        const progressFunctionName = `${mapping.serverName}_function_progress`;
        const progressResult = await this.callTool({
          functionNameWithPrefix: progressFunctionName,
          functionCallId: option.functionCallId,
        });
        if (progressResult) {
          option.progressCallback?.(progressResult);
        }
      };
      (async () => {
        while (!done) {
          await new Promise((resolve) => setTimeout(resolve, 5000));
          checkProgressTask();
        }
      })();
    }

    const result = await server.client.callTool({
      name: mapping.functionName,
      arguments: {
        ...(option.functionCallArguments ?? {}),
        $functionCallId: option.functionCallId,
        ...(option.extraArguments ?? {}),
      },
    });
    done = true;

    if (typeof result.content === "object" && result.content instanceof Array) {
      let text = "";
      result.content.forEach((it) => {
        if (it.type === "text") {
          text += it.text + "\n";
        } else {
          text += JSON.stringify(it);
        }
      });
      return text;
    } else {
      return `${JSON.stringify(result.content)}`;
    }
  }
}
