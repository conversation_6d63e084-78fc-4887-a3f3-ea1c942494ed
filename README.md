# Nuro Agent WebUI

开发文档见 https://bytedance.larkoffice.com/wiki/Vg0fw4wrzihN0mk3Yexc05Twnaf

## 项目规范

### 命名规范

1. 所有目录和文件都必须使用中划线命名（kebab-case）

### Next.js 规范

1. 新增页面需要在 `app` 目录下新增目录，目录内必须包含 `page.tsx` 文件
2. 每个页面目录下应包含以下子目录：
   - `components`：存放页面级组件
   - `store`：存放页面级状态管理
   - `utils`：存放页面级工具函数

### 项目结构

```
src/
└── app/
    ├── [page-name]/
    │   ├── components/
    │   ├── store/
    │   ├── utils/
    │   └── page.tsx
    └── components/
        └── 全局组件
```

### 技术栈

- Next.js 15
- React 19
- TypeScript
- Tailwind CSS
- Jotai (状态管理)

### 开发约定

1. 组件文件使用 `.tsx` 扩展名
2. 工具文件使用 `.ts` 扩展名
3. 组件和页面使用函数式组件
4. 使用 Jotai 进行状态管理
5. 样式优先使用 Tailwind CSS

### TypeScript 最佳实践

1. 严格模式：项目启用 TypeScript 严格模式 (`strict: true`)，确保类型安全
2. 类型定义：
   - 显式声明组件 Props 类型
   - 使用联合类型约束特定值域
   - 合理使用泛型增强类型安全
3. 状态管理：
   - 使用 `useState<T>` 显式声明状态类型
   - 对于可能为空的状态，使用联合类型如 `useState<User | null>(null)`
4. 类型组织：
   - 全局类型定义放在 `src/types` 目录下
   - 组件相关类型在组件文件中定义或就近维护

### React 最佳实践

1. 组件设计：
   - 优先使用函数式组件和 Hooks
   - 合理拆分组件，保持组件单一职责
   - 使用 React.FC 泛型定义组件类型
2. 状态管理：
   - 组件内部状态使用 useState/useReducer
   - 全局状态使用 Jotai
3. 性能优化：
   - 使用 React.memo 优化函数组件重渲染
   - 使用 useMemo/useCallback 缓存计算结果和回调函数
   - 合理使用动态导入进行代码分割

### 代码质量

1. 代码规范：
   - 使用 ESLint 进行代码检查
   - 使用 Prettier 进行代码格式化
   - 遵循 Airbnb 或 Standard JS 规范
2. 代码审查：
   - 提交前确保代码通过 lint 检查
   - 提交信息遵循 conventional commits 规范
3. 测试：
   - 单元测试使用 Jest + React Testing Library
   - E2E 测试使用 Cypress
   - 组件测试覆盖核心功能

### 样式规范

1. 使用 Tailwind CSS 进行样式开发
2. 避免使用内联样式
3. 全局样式在 `globals.css` 中定义
4. 组件样式优先使用 Tailwind CSS 工具类
5. 复杂样式可使用 CSS Modules

### 版本控制

1. 分支管理：
   - 使用 Git Flow 或 GitHub Flow
   - 功能开发在 feature 分支进行
   - 发布使用 release 分支
2. 提交信息：
   - 使用 conventional commits 规范
   - 提交信息应清晰描述变更内容
3. 忽略文件：
   - `node_modules`、`.next` 等构建产物不提交
   - 环境配置文件不提交，使用 `.env.example` 模板