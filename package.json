{"name": "@byted/nuro-agent-webui", "version": "0.1.0", "private": true, "engines": {"node": ">=18.18.0"}, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start -p 32345", "lint": "next lint"}, "dependencies": {"@ai-sdk/openai": "^1.2.2", "@ai-sdk/openai-compatible": "^0.1.13", "@byted-service/tos": "^4.3.8", "@byted/dreamina_agreement_js": "1.0.40-beta.1", "@byted/dreamina_dml_js_web": "1.0.40-beta.1", "@byted/nurosdk-js": "3.0.1-alpha.1", "@byted/uploader": "^2.1.4", "@bytefaas/nodejs-framework-httpnative": "^1.1.0", "@chatui/core": "^2.4.2", "@langchain/core": "^0.3.45", "@microlink/react-json-view": "^1.26.1", "@modelcontextprotocol/sdk": "^1.6.1", "@radix-ui/react-context-menu": "^2.2.6", "@tailwindcss/postcss": "^4.0.13", "@types/axios": "^0.14.4", "ai": "^4.1.54", "axios": "^1.8.3", "fast-json-patch": "^3.1.1", "jotai": "^2.12.5", "next": "15.2.1", "openai": "^4.86.1", "postcss": "^8.5.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "remark-gfm": "^4.0.1", "tailwindcss": "^4.0.13"}, "devDependencies": {"@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "typescript": "^5"}}